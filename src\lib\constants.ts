import { Card, PokerPosition } from '@/types'

// Constantes do poker
export const SUITS = ['hearts', 'diamonds', 'clubs', 'spades'] as const
export const RANKS = ['A', 'K', 'Q', 'J', 'T', '9', '8', '7', '6', '5', '4', '3', '2'] as const

export const SUIT_SYMBOLS = {
  hearts: '♥',
  diamonds: '♦',
  clubs: '♣',
  spades: '♠'
} as const

export const SUIT_COLORS = {
  hearts: 'text-red-500',
  diamonds: 'text-red-500',
  clubs: 'text-black',
  spades: 'text-black'
} as const

export const POSITIONS: PokerPosition[] = [
  { name: 'UTG', displayName: 'Under The Gun', order: 1 },
  { name: 'MP', displayName: 'Middle Position', order: 2 },
  { name: 'CO', displayName: 'Cut Off', order: 3 },
  { name: '<PERSON><PERSON>', displayName: 'But<PERSON>', order: 4 },
  { name: 'S<PERSON>', displayName: 'Small Blind', order: 5 },
  { name: 'BB', displayName: 'Big Blind', order: 6 }
]

export const POSITION_COLORS = {
  UTG: 'bg-red-600',
  MP: 'bg-orange-600',
  CO: 'bg-yellow-600',
  BTN: 'bg-green-600',
  SB: 'bg-blue-600',
  BB: 'bg-purple-600'
} as const

// Níveis e XP
export const LEVELS = [
  { level: 1, xpRequired: 0, name: 'Novice', nameEn: 'Novice', namePt: 'Novato' },
  { level: 2, xpRequired: 100, name: 'Beginner', nameEn: 'Beginner', namePt: 'Iniciante' },
  { level: 3, xpRequired: 300, name: 'Amateur', nameEn: 'Amateur', namePt: 'Amador' },
  { level: 4, xpRequired: 600, name: 'Intermediate', nameEn: 'Intermediate', namePt: 'Intermediário' },
  { level: 5, xpRequired: 1000, name: 'Advanced', nameEn: 'Advanced', namePt: 'Avançado' },
  { level: 6, xpRequired: 1500, name: 'Expert', nameEn: 'Expert', namePt: 'Especialista' },
  { level: 7, xpRequired: 2100, name: 'Master', nameEn: 'Master', namePt: 'Mestre' },
  { level: 8, xpRequired: 2800, name: 'Grandmaster', nameEn: 'Grandmaster', namePt: 'Grão-mestre' }
]

// Tamanhos de aposta padrão
export const BET_SIZINGS = {
  small: 0.33,
  medium: 0.66,
  large: 1.0,
  overbet: 1.5
} as const

// Ranges de mãos pré-flop
export const PREFLOP_RANGES = {
  premium: ['AA', 'KK', 'QQ', 'JJ', 'AKs', 'AKo'],
  strong: ['TT', '99', 'AQs', 'AQo', 'AJs', 'AJo', 'KQs', 'KQo'],
  medium: ['88', '77', 'ATs', 'ATo', 'A9s', 'KJs', 'KJo', 'KTs', 'QJs', 'QJo'],
  weak: ['66', '55', '44', '33', '22', 'A8s', 'A7s', 'A6s', 'A5s', 'A4s', 'A3s', 'A2s'],
  suited_connectors: ['JTs', 'T9s', '98s', '87s', '76s', '65s', '54s'],
  offsuit_broadways: ['KTo', 'QTo', 'JTo', 'Q9o', 'J9o', 'T9o']
}

// Categorias de cenários GTO
export const GTO_CATEGORIES = [
  'preflop_opening',
  'preflop_3bet',
  'preflop_4bet',
  'cbet_flop',
  'cbet_turn',
  'cbet_river',
  'check_raise',
  'barrel_bluff',
  'value_betting',
  'pot_control',
  'river_decision'
] as const

// Configurações de dificuldade
export const DIFFICULTY_SETTINGS = {
  beginner: {
    timeLimit: 30,
    hintsAllowed: 3,
    explanationDetail: 'detailed'
  },
  intermediate: {
    timeLimit: 20,
    hintsAllowed: 1,
    explanationDetail: 'moderate'
  },
  advanced: {
    timeLimit: 15,
    hintsAllowed: 0,
    explanationDetail: 'brief'
  }
} as const

// Conquistas padrão
export const DEFAULT_ACHIEVEMENTS = [
  {
    id: 'first_session',
    nameEn: 'First Steps',
    namePt: 'Primeiros Passos',
    descriptionEn: 'Complete your first training session',
    descriptionPt: 'Complete sua primeira sessão de treino',
    icon: '🎯',
    xpReward: 50,
    category: 'training',
    requirement: { sessions: 1 }
  },
  {
    id: 'accuracy_master',
    nameEn: 'Accuracy Master',
    namePt: 'Mestre da Precisão',
    descriptionEn: 'Achieve 90% accuracy in 10 consecutive sessions',
    descriptionPt: 'Alcance 90% de precisão em 10 sessões consecutivas',
    icon: '🎯',
    xpReward: 200,
    category: 'accuracy',
    requirement: { accuracy: 0.9, consecutive: 10 }
  },
  {
    id: 'streak_warrior',
    nameEn: 'Streak Warrior',
    namePt: 'Guerreiro da Sequência',
    descriptionEn: 'Get 20 correct answers in a row',
    descriptionPt: 'Acerte 20 respostas seguidas',
    icon: '🔥',
    xpReward: 150,
    category: 'streak',
    requirement: { streak: 20 }
  },
  {
    id: 'level_5',
    nameEn: 'Advanced Player',
    namePt: 'Jogador Avançado',
    descriptionEn: 'Reach level 5',
    descriptionPt: 'Alcance o nível 5',
    icon: '⭐',
    xpReward: 300,
    category: 'level',
    requirement: { level: 5 }
  }
]

// Configurações de animação
export const ANIMATION_DURATIONS = {
  cardFlip: 600,
  chipMove: 800,
  fadeIn: 300,
  slideUp: 400
} as const
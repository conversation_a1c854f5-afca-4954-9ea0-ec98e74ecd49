@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-900 text-white;
  }
  
  * {
    @apply border-gray-700;
  }
}

@layer components {
  .poker-table {
    background: radial-gradient(ellipse at center, #0F5132 0%, #0A3D26 100%);
    border: 8px solid #8B4513;
    box-shadow: 
      inset 0 0 50px rgba(0,0,0,0.5),
      0 0 30px rgba(0,0,0,0.3);
  }
  
  .poker-card {
    @apply bg-white text-black rounded-lg shadow-lg border-2 border-gray-300;
    aspect-ratio: 5/7;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 
      0 4px 8px rgba(0,0,0,0.1),
      0 2px 4px rgba(0,0,0,0.06);
  }
  
  .poker-card-back {
    @apply bg-blue-800 rounded-lg shadow-lg border-2 border-blue-900;
    aspect-ratio: 5/7;
    background: 
      linear-gradient(145deg, #1e40af 0%, #1e3a8a 100%),
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.1) 10px,
        rgba(255,255,255,0.1) 20px
      );
  }
  
  .chip {
    @apply rounded-full border-4 shadow-lg relative;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 50%);
  }
  
  .chip::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2), transparent 70%);
  }
  
  .chip-red {
    @apply bg-red-600 border-red-800;
    background: radial-gradient(circle at center, #dc2626 0%, #991b1b 100%);
  }
  
  .chip-green {
    @apply bg-green-600 border-green-800;
    background: radial-gradient(circle at center, #16a34a 0%, #166534 100%);
  }
  
  .chip-blue {
    @apply bg-blue-600 border-blue-800;
    background: radial-gradient(circle at center, #2563eb 0%, #1e40af 100%);
  }
  
  .chip-black {
    @apply bg-gray-800 border-gray-900;
    background: radial-gradient(circle at center, #374151 0%, #111827 100%);
  }
  
  .chip-yellow {
    @apply bg-yellow-500 border-yellow-700;
    background: radial-gradient(circle at center, #eab308 0%, #a16207 100%);
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 shadow-lg;
  }
  
  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 shadow-lg;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 shadow-lg;
  }
  
  .btn-warning {
    @apply bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 shadow-lg;
  }
  
  .card-hover {
    @apply transition-all duration-200 hover:scale-105 hover:shadow-xl cursor-pointer;
  }
  
  .glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  
  .glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  }
  
  .glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
  }
  
  .pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .floating-animation {
    animation: float 3s ease-in-out infinite;
  }
  
  .table-felt {
    background: 
      radial-gradient(ellipse at center, #0F5132 0%, #0A3D26 100%),
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.03) 2px,
        rgba(255,255,255,0.03) 4px
      );
  }
  
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .neon-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6) 1;
    animation: neon-glow 2s ease-in-out infinite alternate;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  }
  
  .text-shadow-lg {
    text-shadow: 4px 4px 8px rgba(0,0,0,0.5);
  }
  
  .gradient-text {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-text-green {
    background: linear-gradient(45deg, #10b981, #34d399);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-text-red {
    background: linear-gradient(45deg, #ef4444, #f87171);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  .animate-card-flip {
    animation: cardFlip 0.6s ease-in-out;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes bounceIn {
  0% { 
    transform: scale(0.3); 
    opacity: 0; 
  }
  50% { 
    transform: scale(1.05); 
  }
  70% { 
    transform: scale(0.9); 
  }
  100% { 
    transform: scale(1); 
    opacity: 1; 
  }
}

@keyframes cardFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes neon-glow {
  from {
    box-shadow: 0 0 5px #3b82f6, 0 0 10px #3b82f6, 0 0 15px #3b82f6;
  }
  to {
    box-shadow: 0 0 10px #8b5cf6, 0 0 20px #8b5cf6, 0 0 30px #8b5cf6;
  }
}

/* Responsividade customizada */
@media (max-width: 640px) {
  .poker-table {
    border-width: 4px;
  }
  
  .poker-card {
    aspect-ratio: 5/7;
  }
}

/* Melhorias de acessibilidade */
@media (prefers-reduced-motion: reduce) {
  .card-hover {
    transition: none;
  }
  
  .pulse-animation,
  .floating-animation {
    animation: none;
  }
}

/* Tema escuro aprimorado */
.dark {
  color-scheme: dark;
}

/* Estilos para elementos de formulário */
input[type="email"],
input[type="password"],
input[type="text"],
textarea,
select {
  @apply bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

input[type="email"]:focus,
input[type="password"]:focus,
input[type="text"]:focus,
textarea:focus,
select:focus {
  @apply ring-2 ring-blue-500 border-transparent;
}

/* Estilos para loading states */
.loading-spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 2px solid #ffffff;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para tooltips */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-sm text-white bg-gray-800 rounded shadow-lg;
}

/* Estilos para modais */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4;
}
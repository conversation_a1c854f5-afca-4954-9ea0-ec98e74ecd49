import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'Nenhum arquivo enviado' },
        { status: 400 }
      )
    }

    if (!file.name.endsWith('.json')) {
      return NextResponse.json(
        { error: 'Apenas arquivos .json são aceitos' },
        { status: 400 }
      )
    }

    const fileContent = await file.text()
    let scenarios

    try {
      scenarios = JSON.parse(fileContent)
    } catch (error) {
      return NextResponse.json(
        { error: 'Arquivo JSON inválido' },
        { status: 400 }
      )
    }

    if (!Array.isArray(scenarios)) {
      return NextResponse.json(
        { error: 'O arquivo deve conter um array de cenários' },
        { status: 400 }
      )
    }

    const results = {
      success: 0,
      errors: [] as string[]
    }

    for (let i = 0; i < scenarios.length; i++) {
      const scenario = scenarios[i]
      
      try {
        // Validar campos obrigatórios
        const requiredFields = ['name', 'description', 'gameType', 'street', 'position', 'playerCards', 'correctAction', 'explanation']
        for (const field of requiredFields) {
          if (!scenario[field]) {
            throw new Error(`Campo obrigatório ausente: ${field}`)
          }
        }

        // Validar estrutura das cartas
        if (!scenario.playerCards.card1 || !scenario.playerCards.card2) {
          throw new Error('playerCards deve ter card1 e card2')
        }

        // Validar ações disponíveis
        if (!scenario.availableActions || !Array.isArray(scenario.availableActions) || scenario.availableActions.length === 0) {
          throw new Error('availableActions deve ser um array não vazio')
        }

        // Processar filtro personalizado se fornecido
        let customFilterId = null
        if (scenario.customFilter) {
          // Verificar se o filtro existe
          let customFilter = await prisma.customFilter.findUnique({
            where: { name: scenario.customFilter }
          })

          // Se não existe, criar automaticamente
          if (!customFilter) {
            customFilter = await prisma.customFilter.create({
              data: {
                name: scenario.customFilter,
                description: scenario.customFilterDescription || `Filtro personalizado: ${scenario.customFilter}`,
                color: scenario.customFilterColor || '#3B82F6'
              }
            })
          }

          customFilterId = customFilter.id
        }

        // Criar o cenário
        await prisma.gTOScenario.create({
          data: {
            name: scenario.name,
            nameEn: scenario.nameEn || scenario.name,
            namePt: scenario.namePt || scenario.name,
            description: scenario.description,
            descriptionEn: scenario.descriptionEn || scenario.description,
            descriptionPt: scenario.descriptionPt || scenario.description,
            gameType: scenario.gameType,
            street: scenario.street,
            position: scenario.position,
            stackDepth: scenario.stackDepth || 'deep',
            playerCards: JSON.stringify(scenario.playerCards),
            communityCards: JSON.stringify(scenario.communityCards || { flop: null, turn: null, river: null }),
            potSize: parseFloat(scenario.potSize) || 0,
            playerStack: parseFloat(scenario.playerStack) || 200,
            opponentActions: JSON.stringify(scenario.opponentActions || []),
            availableActions: JSON.stringify(scenario.availableActions),
            correctAction: scenario.correctAction,
            equity: parseFloat(scenario.equity) || 0.5,
            frequency: parseFloat(scenario.frequency) || 1.0,
            explanation: scenario.explanation,
            explanationEn: scenario.explanationEn || scenario.explanation,
            explanationPt: scenario.explanationPt || scenario.explanation,
            context: JSON.stringify(scenario.context || {}),
            difficulty: scenario.difficulty || 'intermediate',
            category: scenario.category || 'general',
            tags: JSON.stringify(scenario.tags || []),
            customFilterId: customFilterId
          }
        })

        results.success++
      } catch (error) {
        results.errors.push(`Cenário ${i + 1} (${scenario.name || 'sem nome'}): ${(error as Error).message}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: `${results.success} cenários importados com sucesso`,
      results
    })

  } catch (error) {
    console.error('Erro no upload:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
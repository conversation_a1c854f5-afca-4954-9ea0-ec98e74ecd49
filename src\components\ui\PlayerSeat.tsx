'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface PlayerSeatProps {
  position: string
  isActive: boolean
  stack?: number
  showCards?: boolean
  cards?: any[]
  x: string
  y: string
}

export default function PlayerSeat({ 
  position, 
  isActive, 
  stack, 
  showCards = false, 
  cards = [], 
  x, 
  y 
}: PlayerSeatProps) {
  return (
    <div
      className="absolute transform -translate-x-1/2 -translate-y-1/2 z-20"
      style={{ left: x, top: y }}
    >
      <div className="flex flex-col items-center space-y-2">
        {/* Indicador de posição */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className={cn(
            'px-3 py-1 rounded-full text-white text-sm font-bold shadow-lg border-2',
            isActive 
              ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 border-yellow-300 shadow-yellow-400/50' 
              : 'bg-gradient-to-r from-gray-600 to-gray-700 border-gray-500'
          )}
        >
          {position}
        </motion.div>
        
        {/* Stack do jogador */}
        {isActive && stack && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-800 rounded-lg px-3 py-1 border border-gray-600 shadow-lg"
          >
            <div className="text-white text-sm font-semibold">
              ${stack.toLocaleString()}
            </div>
          </motion.div>
        )}

        {/* Cartas do jogador (se aplicável) */}
        {showCards && cards.length > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
            className="flex space-x-1"
          >
            {cards.map((card, index) => (
              <div
                key={index}
                className="w-8 h-11 bg-white rounded border border-gray-300 shadow-sm flex items-center justify-center text-xs"
              >
                <span className={card.suit === 'hearts' || card.suit === 'diamonds' ? 'text-red-500' : 'text-black'}>
                  {card.rank}
                </span>
              </div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  )
}
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// POST - Verificar se filtro existe e criar se necessário
export async function POST(request: NextRequest) {
  try {
    const { name, description, color, autoCreate } = await request.json()

    if (!name) {
      return NextResponse.json(
        { error: 'Nome do filtro é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se já existe
    let filter = await prisma.customFilter.findUnique({
      where: { name }
    })

    if (filter) {
      return NextResponse.json({
        success: true,
        exists: true,
        filter
      })
    }

    // Se não existe e autoCreate é true, criar automaticamente
    if (autoCreate) {
      filter = await prisma.customFilter.create({
        data: {
          name,
          description: description || `Filtro personalizado: ${name}`,
          color: color || '#3B82F6'
        }
      })

      return NextResponse.json({
        success: true,
        exists: false,
        created: true,
        filter,
        message: `Filtro personalizado "${name}" criado automaticamente`
      })
    }

    // Se não existe e não deve criar automaticamente
    return NextResponse.json({
      success: true,
      exists: false,
      created: false,
      message: `Filtro "${name}" não existe`
    })

  } catch (error) {
    console.error('Erro ao verificar filtro:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
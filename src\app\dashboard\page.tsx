'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Play, 
  Target, 
  Zap, 
  Star,
  TrendingUp,
  Clock,
  Award,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useLanguage } from '@/hooks/useLanguage'
import { User } from '@/types'
import { getLevelFromXP, getXPForNextLevel, getLevelProgress } from '@/lib/utils'

export default function DashboardPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [stats, setStats] = useState({
    totalSessions: 0,
    averageAccuracy: 0,
    currentStreak: 0,
    totalHands: 0
  })

  useEffect(() => {
    // Carregar usuário padrão
    loadDefaultUser()
  }, [])

  const loadDefaultUser = async () => {
    try {
      // Usar ID do usuário padrão criado
      const defaultUserId = 'cmdhptt1n0000m0rwt9dn15fu'
      
      // Carregar dados do usuário
      const userResponse = await fetch(`/api/user/profile/${defaultUserId}`)
      if (userResponse.ok) {
        const userData = await userResponse.json()
        setUser(userData)
        
        // Carregar estatísticas
        const statsResponse = await fetch(`/api/user/stats/${defaultUserId}`)
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const currentLevel = getLevelFromXP(user.xp)
  const xpForNext = getXPForNextLevel(user.xp)
  const levelProgress = getLevelProgress(user.xp)

  const trainingModes = [
    {
      title: t('training.quiz'),
      description: t('dashboard.quickTest'),
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      href: '/dashboard/training/quiz',
      duration: '5-10 min'
    },
    {
      title: t('training.simulation'),
      description: t('dashboard.fullSimulation'),
      icon: Play,
      color: 'from-green-500 to-green-600',
      href: '/dashboard/training/simulation',
      duration: '15-30 min'
    },
    {
      title: t('training.practice'),
      description: t('dashboard.freePractice'),
      icon: Zap,
      color: 'from-purple-500 to-purple-600',
      href: '/dashboard/training/practice',
      duration: t('dashboard.unlimited')
    }
  ]

  const quickStats = [
    {
      label: t('profile.level'),
      value: currentLevel,
      icon: Star,
      color: 'text-yellow-400'
    },
    {
      label: t('statistics.averageEquity'),
      value: `${stats.averageAccuracy.toFixed(1)}%`,
      icon: Target,
      color: 'text-green-400'
    },
    {
      label: t('statistics.currentStreak'),
      value: stats.currentStreak,
      icon: TrendingUp,
      color: 'text-blue-400'
    },
    {
      label: t('statistics.totalHands'),
      value: stats.totalHands,
      icon: BarChart3,
      color: 'text-purple-400'
    }
  ]

  return (
    <div className="p-6">
        {/* Progresso do usuário */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-white mb-1">
                {t('dashboard.level')} {currentLevel}
              </h2>
              <p className="text-blue-100">
                {xpForNext > 0 ? `${xpForNext} ${t('dashboard.xpToNextLevel')}` : t('dashboard.maxLevelReached')}
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-white">{user.xp}</div>
              <div className="text-blue-100 text-sm">{t('dashboard.totalXP')}</div>
            </div>
          </div>
          
          {/* Barra de progresso */}
          <div className="w-full bg-blue-800 rounded-full h-3">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${levelProgress}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              className="bg-yellow-400 h-3 rounded-full"
            />
          </div>
        </motion.div>

        {/* Estatísticas rápidas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-800 rounded-xl p-6 border border-gray-700"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm mb-1">{stat.label}</p>
                  <p className="text-2xl font-bold text-white">{stat.value}</p>
                </div>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Modos de treinamento */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">
            {t('dashboard.chooseTraining')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {trainingModes.map((mode, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="bg-gray-800 rounded-xl overflow-hidden border border-gray-700 hover:border-blue-500 transition-colors"
              >
                <div className={`h-2 bg-gradient-to-r ${mode.color}`} />
                
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${mode.color} rounded-lg flex items-center justify-center mr-4`}>
                      <mode.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white">{mode.title}</h3>
                      <div className="flex items-center text-gray-400 text-sm">
                        <Clock className="w-4 h-4 mr-1" />
                        {mode.duration}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-400 mb-6">{mode.description}</p>
                  
                  <Link
                    href={mode.href}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
                  >
                    {t('dashboard.start')}
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Atividade recente */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">{t('dashboard.recentActivity')}</h2>
            <Link href="/dashboard/statistics" className="text-blue-400 hover:text-blue-300 text-sm">
              {t('dashboard.viewAll')}
            </Link>
          </div>
          
          <div className="space-y-4">
            {/* Placeholder para atividades recentes */}
            <div className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
              <Award className="w-8 h-8 text-yellow-400" />
              <div>
                <p className="text-white font-medium">{t('dashboard.welcomeMessage')}</p>
                <p className="text-gray-400 text-sm">{t('dashboard.startFirstTraining')}</p>
              </div>
            </div>
          </div>
        </motion.div>
    </div>
  )
}
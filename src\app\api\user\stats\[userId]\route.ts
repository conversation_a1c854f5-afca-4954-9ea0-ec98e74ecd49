import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Buscar estatísticas do usuário
    const userStats = await prisma.userStatistics.findUnique({
      where: { userId: params.userId }
    })

    // Buscar sessões de treinamento
    const sessions = await prisma.trainingSession.findMany({
      where: { userId: params.userId },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    // Calcular estatísticas agregadas
    const totalSessions = sessions.length
    const completedSessions = sessions.filter(s => s.completed)
    const averageAccuracy = completedSessions.length > 0 
      ? completedSessions.reduce((acc, s) => acc + (s.correctAnswers / s.totalQuestions), 0) / completedSessions.length * 100
      : 0

    const stats = {
      totalSessions,
      averageAccuracy,
      currentStreak: userStats?.currentStreak || 0,
      longestStreak: userStats?.longestStreak || 0,
      totalHands: userStats?.totalHands || 0,
      correctDecisions: userStats?.correctDecisions || 0,
      preflopAccuracy: userStats?.preflopAccuracy || 0,
      flopAccuracy: userStats?.flopAccuracy || 0,
      turnAccuracy: userStats?.turnAccuracy || 0,
      riverAccuracy: userStats?.riverAccuracy || 0,
      aggressionFrequency: userStats?.aggressionFrequency || 0,
      foldFrequency: userStats?.foldFrequency || 0,
      callFrequency: userStats?.callFrequency || 0,
      raiseFrequency: userStats?.raiseFrequency || 0,
      cBetFrequency: userStats?.cBetFrequency || 0,
      checkRaiseFrequency: userStats?.checkRaiseFrequency || 0,
      recentSessions: sessions.slice(0, 5)
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
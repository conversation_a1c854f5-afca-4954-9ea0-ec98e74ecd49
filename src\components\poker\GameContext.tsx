'use client'

import { motion } from 'framer-motion'
import { GameState } from '@/types'
import { Info, Users, DollarSign, Target } from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'

interface GameContextProps {
  gameState: GameState
}

export default function GameContext({ gameState }: GameContextProps) {
  const { t } = useLanguage()
  const { street, pot, gameType, playerActions, position } = gameState

  // Calcular informações contextuais
  const activePlayers = playerActions.filter(p => p.isActive).length + 1 // +1 para o usuário
  const totalInvested = playerActions.reduce((sum, p) => sum + (p.amount || 0), 0)
  const potOdds = totalInvested > 0 ? ((pot - totalInvested) / totalInvested * 100).toFixed(1) : '0'
  
  // Determinar se há agressão na mesa
  const hasAggression = playerActions.some(p => p.action.type === 'raise' || p.action.type === 'bet')
  const lastAggressor = playerActions.find(p => p.action.type === 'raise' || p.action.type === 'bet')

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 rounded-xl p-4 border border-gray-700 mb-6"
    >
      <h3 className="text-white font-semibold mb-4 flex items-center">
        <Info className="w-5 h-5 mr-2 text-blue-400" />
        {t('poker.situationContext')}
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Tipo de jogo e street */}
        <div className="bg-gray-700/50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <Target className="w-4 h-4 text-green-400 mr-2" />
            <span className="text-green-400 text-sm font-medium">{t('poker.situation')}</span>
          </div>
          <div className="text-white text-sm">
            <div>{gameType === 'cash' ? t('poker.cashGame') : t('poker.tournament')}</div>
            <div className="text-gray-300 capitalize">{t(`streets.${street}`)}</div>
          </div>
        </div>

        {/* Jogadores ativos */}
        <div className="bg-gray-700/50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <Users className="w-4 h-4 text-blue-400 mr-2" />
            <span className="text-blue-400 text-sm font-medium">{t('poker.players')}</span>
          </div>
          <div className="text-white text-sm">
            <div>{activePlayers} {t('poker.active')}</div>
            <div className="text-gray-300">{t('poker.ofTable')}</div>
          </div>
        </div>

        {/* Pot Odds */}
        <div className="bg-gray-700/50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <DollarSign className="w-4 h-4 text-yellow-400 mr-2" />
            <span className="text-yellow-400 text-sm font-medium">{t('poker.potOdds')}</span>
          </div>
          <div className="text-white text-sm">
            <div>{potOdds}%</div>
            <div className="text-gray-300">${totalInvested} {t('poker.invested')}</div>
          </div>
        </div>

        {/* Agressão */}
        <div className="bg-gray-700/50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <div className={`w-4 h-4 rounded-full mr-2 ${hasAggression ? 'bg-red-400' : 'bg-green-400'}`} />
            <span className={`text-sm font-medium ${hasAggression ? 'text-red-400' : 'text-green-400'}`}>
              {t('poker.aggression')}
            </span>
          </div>
          <div className="text-white text-sm">
            {hasAggression ? (
              <>
                <div>{t('poker.present')}</div>
                <div className="text-gray-300">{lastAggressor?.position}</div>
              </>
            ) : (
              <>
                <div>{t('poker.absent')}</div>
                <div className="text-gray-300">{t('poker.passiveTable')}</div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Dica contextual */}
      <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <div className="text-blue-300 text-sm font-medium mb-1">{t('poker.gtoTip')}</div>
        <div className="text-blue-200 text-sm">
          {getContextualTip(gameState, t)}
        </div>
      </div>
    </motion.div>
  )
}

function getContextualTip(gameState: GameState, t: any): string {
  const { street, position, playerActions } = gameState
  const hasAggression = playerActions.some(p => p.action.type === 'raise' || p.action.type === 'bet')
  const activePlayers = playerActions.filter(p => p.isActive).length + 1

  if (street === 'preflop') {
    if (position.name === 'BTN' || position.name === 'CO') {
      return t('poker.tips.latePosition')
    } else if (position.name === 'UTG' || position.name === 'MP') {
      return t('poker.tips.earlyPosition')
    } else {
      return t('poker.tips.blinds')
    }
  }

  if (hasAggression) {
    return t('poker.tips.withAggression')
  }

  if (activePlayers <= 3) {
    return t('poker.tips.fewPlayers')
  }

  return t('poker.tips.analyzeBoard')
}
import { NextRequest, NextResponse } from 'next/server'
import { getFilteredScenarios } from '@/lib/scenario-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const filters = {
      street: searchParams.get('street') as any,
      position: searchParams.get('position') as any,
      difficulty: searchParams.get('difficulty') as any,
      category: searchParams.get('category') as any,
      gameType: searchParams.get('gameType') as any,
      customFilter: searchParams.get('customFilter') as any
    }

    // Remover filtros vazios
    Object.keys(filters).forEach(key => {
      if (!filters[key as keyof typeof filters] || filters[key as keyof typeof filters] === 'all') {
        delete filters[key as keyof typeof filters]
      }
    })

    const count = parseInt(searchParams.get('count') || '5')
    const scenarios = await getFilteredScenarios(filters, count)
    
    return NextResponse.json({ 
      success: true, 
      scenarios,
      filters: filters,
      count: scenarios.length
    })
  } catch (error) {
    console.error('Erro ao buscar cenários filtrados:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao carregar cenários filtrados' },
      { status: 500 }
    )
  }
}
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function checkActions() {
  try {
    const scenarios = await prisma.gTOScenario.findMany()
    
    console.log('🔍 Verificando ações nos cenários...\n')
    
    scenarios.forEach((scenario, index) => {
      console.log(`📋 Cenário ${index + 1}: ${scenario.name}`)
      
      try {
        const opponentActions = JSON.parse(scenario.opponentActions)
        console.log('  Ações dos oponentes:')
        opponentActions.forEach(action => {
          console.log(`    ${action.position}: "${action.action}" (${action.isActive ? 'ativo' : 'inativo'})`)
        })
      } catch (e) {
        console.log('  ❌ Erro ao parsear opponentActions')
      }
      
      console.log('')
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkActions()
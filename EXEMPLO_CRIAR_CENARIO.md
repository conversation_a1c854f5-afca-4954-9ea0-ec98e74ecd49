# � EXEM PLO PRÁTICO: COMO CRIAR UM NOVO CENÁRIO

## 🎯 **CENÁRIO EXEMPLO: "Turn Check-Raise com Flush Draw"**

### **Situação:**
- Posição: Big Blind (BB)
- Street: Turn
- Cartas: 9♥ 8♥
- Board: 7♥ 6♣ 2♦ 5♠
- Ação do oponente: Bet $30
- Decisão: Check-raise com combo draw

---

## � **PAMSSO A PASSO PARA ADICIONAR**

### **1. Definir os Dados do Cenário**
```typescript
const newScenario = {
  // Identificação única
  id: "scenario_005_turn_combo_draw_checkraise",
  
  // Nomes em diferentes idiomas
  name: "Turn Check-Raise with Combo Draw",
  nameEn: "Turn Check-Raise with Combo Draw", 
  namePt: "Check-Raise no Turn com Combo Draw",
  
  // Descrições
  description: "Check-raise opportunity on turn with combo draw",
  descriptionEn: "Check-raise opportunity on turn with combo draw",
  descriptionPt: "Oportunidade de check-raise no turn com combo draw",
  
  // Configurações básicas
  gameType: "cash",
  street: "turn",
  position: "BB",
  stackDepth: "deep",
  
  // Cartas do jogador (JSON)
  playerCards: JSON.stringify({ 
    card1: "9h", 
    card2: "8h" 
  }),
  
  // Cartas comunitárias (JSON)
  communityCards: JSON.stringify({
    flop: ["7h", "6c", "2d"],
    turn: "5s"
  }),
  
  // Estado da mesa
  potSize: 45.0,
  playerStack: 170.0,
  
  // Ações dos oponentes (JSON)
  opponentActions: JSON.stringify([
    { 
      position: "BTN", 
      action: "bet", 
      amount: 30, 
      isActive: true, 
      stackSize: 164 
    }
  ]),
  
  // AÇÕES DISPONÍVEIS (Parte mais importante!)
  availableActions: JSON.stringify([
    {
      action: "fold",
      isCorrect: false,
      frequency: 0.10,
      explanation: "Fold desperdiça mão com muita equidade.",
      explanationEn: "Fold wastes hand with lots of equity.",
      explanationPt: "Fold desperdiça mão com muita equidade."
    },
    {
      action: "call",
      amount: 30,
      isCorrect: false,
      frequency: 0.50,
      explanation: "Call é passivo demais com tanto equity.",
      explanationEn: "Call is too passive with so much equity.",
      explanationPt: "Call é passivo demais com tanto equity."
    },
    {
      action: "raise",
      amount: 90,
      sizing: "large",
      isCorrect: true,
      frequency: 0.40,
      explanation: "Check-raise com combo draw - 13 outs para nuts.",
      explanationEn: "Check-raise with combo draw - 13 outs to nuts.",
      explanationPt: "Check-raise com combo draw - 13 outs para nuts."
    }
  ]),
  
  // Ação correta
  correctAction: "raise",
  
  // Métricas GTO
  equity: 0.52,
  frequency: 0.40,
  
  // Explicação detalhada
  explanation: "Com 98h em 7-6-2-5, temos straight draw + flush draw + overcards = 13 outs. Check-raise é correto com tanta equidade.",
  explanationEn: "With 98h on 7-6-2-5, we have straight draw + flush draw + overcards = 13 outs. Check-raise is correct with so much equity.",
  explanationPt: "Com 98h em 7-6-2-5, temos straight draw + flush draw + overcards = 13 outs. Check-raise é correto com tanta equidade.",
  
  // Contexto adicional (JSON)
  context: JSON.stringify({
    spr: 3.6,
    boardTexture: "coordinated",
    handStrength: "draw",
    outs: 13,
    drawType: "combo_draw"
  }),
  
  // Classificação
  difficulty: "intermediate",
  category: "check_raise",
  tags: JSON.stringify(["turn_play", "combo_draw", "semi_bluff", "check_raise"])
}
```

### **2. Adicionar ao Script de População**
```typescript
// Arquivo: scripts/populate-real-scenarios.ts

const realGTOScenarios = [
  // ... cenários existentes ...
  
  // NOVO CENÁRIO AQUI
  {
    id: "scenario_005_turn_combo_draw_checkraise",
    name: "Turn Check-Raise with Combo Draw",
    // ... resto dos dados acima ...
  }
]
```

### **3. Executar o Script**
```bash
# Executar população do banco
npx ts-node scripts/populate-real-scenarios.ts

# Ou se tiver script configurado
npm run populate-scenarios
```

### **4. Verificar se Funcionou**
```bash
# Testar se o cenário aparece no quiz/treinamento
node test-new-scenarios.js
```

---

## 🎮 **COMO O CENÁRIO APARECERÁ NO FRONTEND**

### **Na Mesa de Poker:**
```
┌─────────────────────────────────────────────────────────┐
│                    TURN - BB                            │
│                                                         │
│    7♥  6♣  2♦  5♠                                      │
│                                                         │
│         Suas cartas: 9♥ 8♥                             │
│                                                         │
│    Pot: $45    Stack: $170                             │
│                                                         │
│    Oponente (BTN) apostou $30                          │
│                                                         │
│  [Desistir]  [Pagar $30]  [Aumentar $90]              │
│                              ↑ CORRETO                  │
└─────────────────────────────────────────────────────────┘
```

### **Feedback após Ação:**
```
✅ AÇÃO CORRETA!
Você escolheu: Aumentar $90

🎯 Análise GTO:
- Frequência recomendada: 40%
- Sua equity: 52%
- Explicação: "Check-raise com combo draw - 13 outs para nuts."

💡 Por que é correto:
Com 98h em 7-6-2-5, você tem:
• Straight draw (4, 9, T)
• Flush draw (hearts)  
• Overcards
= 13 outs para mão muito forte

Check-raise aplica pressão máxima com equity suficiente.
```

---

## 🔍 **VALIDAÇÕES AUTOMÁTICAS**

### **O sistema verifica automaticamente:**
```typescript
✅ Cartas válidas (9h, 8h existem no baralho)
✅ Board válido (sem cartas duplicadas)
✅ Frequências somam ~100% (10% + 50% + 40% = 100%)
✅ Tem pelo menos uma ação correta
✅ Pot odds fazem sentido
✅ Stack sizes são realistas
```

---

## 📊 **DADOS GERADOS AUTOMATICAMENTE**

### **Quando o cenário é processado:**
```typescript
// ScenarioService converte automaticamente:

// Entrada (banco):
playerCards: '{"card1":"9h","card2":"8h"}'

// Saída (frontend):
playerCards: [
  { suit: 'hearts', rank: '9', value: 9 },
  { suit: 'hearts', rank: '8', value: 8 }
]

// Entrada (banco):
availableActions: '[{"action":"raise","amount":90}]'

// Saída (frontend):
availableActions: [
  { 
    action: 'raise', 
    amount: 90, 
    label: 'Aumentar', 
    color: 'bg-green-600',
    isCorrect: true 
  }
]
```

---

## 🚀 **VANTAGENS DESTE SISTEMA**

### **Para Desenvolvedores:**
- ✅ **Zero código frontend** necessário
- ✅ **Formato padronizado** para todos os cenários  
- ✅ **Validação automática** de dados
- ✅ **Processamento consistente**

### **Para Criadores de Conteúdo:**
- ✅ **Foco no poker**, não na programação
- ✅ **Flexibilidade total** para qualquer situação
- ✅ **Suporte multilíngue** automático
- ✅ **Categorização** e **tags** para organização

### **Para Usuários:**
- ✅ **Interface consistente** em todos os cenários
- ✅ **Feedback detalhado** e educativo
- ✅ **Progressão natural** por dificuldade
- ✅ **Variedade infinita** de situações

---

## 📈 **ESCALABILIDADE**

### **Facilmente pode crescer para:**
- 🎯 **Milhares de cenários** sem impacto na performance
- 🎯 **Categorias específicas** (6-max, heads-up, tournaments)
- 🎯 **Níveis de dificuldade** granulares
- 🎯 **Situações personalizadas** por usuário
- 🎯 **Geração automática** baseada em ranges GTO

**Status**: ✅ **SISTEMA PRONTO PARA ESCALAR INFINITAMENTE!** 🚀
'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { ArrowLeft, Play, X, Star, Zap } from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'
import PokerTable from '@/components/poker/PokerTable'
import { GameState, PokerAction } from '@/types'
import { getRandomScenarios, ProcessedScenario } from '@/lib/scenario-service'

export default function DemoPage() {
  const { t } = useLanguage()

  // Estados da aplicação
  const [gameState, setGameState] = useState<GameState | null>(null)
  const [showRecommendation, setShowRecommendation] = useState(false)
  const [showRegisterPopup, setShowRegisterPopup] = useState(false)
  const [demoScenarios, setDemoScenarios] = useState<ProcessedScenario[]>([])
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Carregar cenários do banco de dados
  useEffect(() => {
    loadDemoScenarios()
  }, [])

  const loadDemoScenarios = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/scenarios/demo')
      const data = await response.json()

      if (data.success && data.scenarios.length > 0) {
        console.log('✅ Carregando cenários do banco de dados')
        console.log('📋 Primeiro cenário:', data.scenarios[0].name)
        setDemoScenarios(data.scenarios)
        setGameState(data.scenarios[0].gameState)
      } else {
        // Fallback para situações hardcoded se banco estiver vazio
        console.log('⚠️ Usando fallback - banco vazio ou erro')
        setDemoScenarios([])
        setGameState(fallbackGameState)
      }
    } catch (error) {
      console.error('❌ Erro ao carregar cenários demo:', error)
      console.log('⚠️ Usando fallback devido ao erro')
      setGameState(fallbackGameState)
    } finally {
      setIsLoading(false)
    }
  }

  // Fallback para quando banco não estiver disponível  
  const fallbackGameState: GameState = {
    street: 'flop',
    pot: 15,
    communityCards: [
      { suit: 'hearts', rank: 'A', value: 14 },
      { suit: 'clubs', rank: '7', value: 7 },
      { suit: 'diamonds', rank: '2', value: 2 }
    ],
    playerCards: [
      { suit: 'hearts', rank: 'K', value: 13 },
      { suit: 'diamonds', rank: 'Q', value: 12 }
    ],
    position: { name: 'BTN', displayName: 'Button', order: 4 },
    stackSize: 194,
    gameType: 'cash',
    playerActions: [
      { position: 'UTG', action: { type: 'fold' }, isActive: false, stackSize: 200 },
      { position: 'MP', action: { type: 'fold' }, isActive: false, stackSize: 200 },
      { position: 'CO', action: { type: 'fold' }, isActive: false, stackSize: 200 },
      { position: 'SB', action: { type: 'fold' }, isActive: false, stackSize: 199 },
      { position: 'BB', action: { type: 'call' }, amount: 4, isActive: true, stackSize: 194 }
    ],
    actionToUser: true
  }

  const handleAction = (action: PokerAction) => {
    // Atualizar o gameState com a ação do jogador
    setGameState(prev => prev ? ({
      ...prev,
      lastAction: action
    }) : null)
    setShowRecommendation(true)
    // Não remove automaticamente - usuário controla com os botões
  }



  const handleNewSituation = () => {
    // Se temos mais cenários disponíveis, avançar para o próximo
    if (demoScenarios.length > 1 && currentScenarioIndex < demoScenarios.length - 1) {
      const nextIndex = currentScenarioIndex + 1
      setCurrentScenarioIndex(nextIndex)
      setGameState(demoScenarios[nextIndex].gameState)
      setShowRecommendation(false)
      console.log(`✅ Carregando próximo cenário: ${demoScenarios[nextIndex].name}`)
    } else {
      // Se chegou ao último cenário, mostrar popup de registro
      setShowRegisterPopup(true)
    }
  }

  const handleTryAgain = () => {
    setShowRecommendation(false)
    // Limpar a última ação para permitir nova tentativa
    setGameState(prev => prev ? ({
      ...prev,
      lastAction: undefined
    }) : null)
  }

  // Função para obter a recomendação atual
  const getCurrentRecommendation = () => {
    if (demoScenarios.length > 0 && demoScenarios[currentScenarioIndex]) {
      return demoScenarios[currentScenarioIndex].recommendation
    }
    // Fallback para situações hardcoded
    return {
      action: { type: 'bet' as const, amount: 10, sizing: 'medium' as const },
      frequency: 0.80,
      equity: 0.35,
      explanation: 'Com KQ no flop A-7-2, você tem overcards e posição. Este é um spot perfeito para continuation bet - o board seco favorece seu range de abertura e você pode fazer o oponente foldar mãos piores como pares pequenos e draws fracos.'
    }
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center text-blue-400 hover:text-blue-300">
            <ArrowLeft className="w-5 h-5 mr-2" />
            {t('demo.back')}
          </Link>

          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">{t('demo.title')}</h1>
            <p className="text-gray-400">{t('demo.subtitle')}</p>
          </div>

          <Link href="/auth/register" className="btn-primary">
            {t('demo.createAccount')}
          </Link>
        </div>
      </header>

      <div className="max-w-6xl mx-auto p-6">
        {/* Instruções */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-900/30 border border-blue-500/30 rounded-xl p-6 mb-8"
        >
          <div className="flex items-start space-x-4">
            <Play className="w-6 h-6 text-blue-400 flex-shrink-0 mt-1" />
            <div>
              <h2 className="text-xl font-bold text-white mb-2">{t('demo.howItWorks')}</h2>
              <p className="text-gray-300 mb-4">
                {t('demo.howItWorksDescription')}
              </p>
              <ul className="text-gray-300 text-sm space-y-1">
                {(t('demo.instructions') as unknown as string[]).map((instruction: string, index: number) => (
                  <li key={index}>• {instruction}</li>
                ))}
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Navegação entre cenários */}
        {demoScenarios.length > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800 border border-gray-700 rounded-xl p-4 mb-6"
          >
            <div className="flex items-center justify-between">
              <div className="text-white">
                <h3 className="font-semibold">
                  {demoScenarios[currentScenarioIndex]?.name || 'Cenário Demo'}
                </h3>
                <p className="text-gray-400 text-sm">
                  Cenário {currentScenarioIndex + 1} de {demoScenarios.length}
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    const prevIndex = currentScenarioIndex === 0 ? demoScenarios.length - 1 : currentScenarioIndex - 1
                    setCurrentScenarioIndex(prevIndex)
                    setGameState(demoScenarios[prevIndex].gameState)
                    setShowRecommendation(false)
                  }}
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
                >
                  ← Anterior
                </button>
                <button
                  onClick={() => {
                    const nextIndex = (currentScenarioIndex + 1) % demoScenarios.length
                    setCurrentScenarioIndex(nextIndex)
                    setGameState(demoScenarios[nextIndex].gameState)
                    setShowRecommendation(false)
                  }}
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
                >
                  Próximo →
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Mesa de Poker Demo */}
        {isLoading ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-white text-lg">Carregando situação demo...</p>
            </div>
          </div>
        ) : gameState ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <PokerTable
              gameState={gameState}
              onAction={handleAction}
              showRecommendation={showRecommendation}
              recommendation={showRecommendation ? getCurrentRecommendation() : undefined}
              availableActions={demoScenarios.length > 0 ? demoScenarios[currentScenarioIndex]?.availableActions : [
                { action: 'check', isCorrect: false, frequency: 0.25 },
                { action: 'bet', isCorrect: true, frequency: 0.75 }
              ]}
              onNewSituation={handleNewSituation}
              onTryAgain={handleTryAgain}
            />
          </motion.div>
        ) : (
          <div className="text-center text-white">
            <p>Erro ao carregar situação demo</p>
          </div>
        )}

        {/* Popup de Registro */}
        <AnimatePresence>
          {showRegisterPopup && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
              onClick={() => setShowRegisterPopup(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-gray-800 rounded-2xl p-8 max-w-md w-full border border-gray-700"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Header do popup */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-white">Desbloqueie Mais!</h2>
                  </div>
                  <button
                    onClick={() => setShowRegisterPopup(false)}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                {/* Conteúdo do popup */}
                <div className="text-center mb-6">
                  <Star className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-3">
                    Quer Ver Mais Situações?
                  </h3>
                  <p className="text-gray-300 mb-4">
                    Crie sua conta gratuita e acesse:
                  </p>

                  <div className="text-left space-y-2 mb-6">
                    <div className="flex items-center text-green-400">
                      <span className="mr-2">✓</span>
                      <span>Centenas de situações GTO reais</span>
                    </div>
                    <div className="flex items-center text-green-400">
                      <span className="mr-2">✓</span>
                      <span>Sistema de progresso e níveis</span>
                    </div>
                    <div className="flex items-center text-green-400">
                      <span className="mr-2">✓</span>
                      <span>Análise detalhada do seu jogo</span>
                    </div>
                    <div className="flex items-center text-green-400">
                      <span className="mr-2">✓</span>
                      <span>Conquistas e ranking global</span>
                    </div>
                  </div>
                </div>

                {/* Botões de ação */}
                <div className="space-y-3">
                  <Link
                    href="/auth/register"
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
                  >
                    <span className="mr-2">🚀</span>
                    Criar Conta Grátis
                  </Link>

                  <Link
                    href="/auth/login"
                    className="w-full bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center"
                  >
                    Já tenho conta
                  </Link>

                  <button
                    onClick={() => setShowRegisterPopup(false)}
                    className="w-full text-gray-400 hover:text-white transition-colors py-2"
                  >
                    Continuar no demo
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-12 text-center"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Pronto para Começar seu Treinamento?
            </h2>
            <p className="text-blue-100 text-lg mb-6">
              Acesse milhares de cenários, acompanhe seu progresso e melhore suas habilidades no poker.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register" className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg text-lg transition-colors">
                Criar Conta Grátis
              </Link>
              <Link href="/auth/login" className="bg-blue-800 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg text-lg transition-colors">
                Já tenho conta
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Recursos */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">10,000+ Cenários</h3>
            <p className="text-gray-400">
              Treine com milhares de situações reais de poker, desde pré-flop até river.
            </p>
          </div>

          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">Análise Detalhada</h3>
            <p className="text-gray-400">
              Receba explicações completas sobre cada decisão e aprenda a teoria por trás das jogadas.
            </p>
          </div>

          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">Progresso Gamificado</h3>
            <p className="text-gray-400">
              Sistema de níveis, conquistas e ranking para manter você motivado a melhorar.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  ArrowLeft, 
  Play, 
  Pause, 
  RotateCcw, 
  Settings,
  Clock,
  Target,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import { useLanguage } from '@/hooks/useLanguage'
import { User, TrainingScenario, PokerAction } from '@/types'
// import Navbar from '@/components/dashboard/Navbar' // Removido - componente não existe
import PokerTable from '@/components/poker/PokerTable'
import { getRandomScenarios } from '@/lib/scenario-service'

export default function SimulationPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [currentScenario, setCurrentScenario] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastScenarioIds, setLastScenarioIds] = useState<string[]>([])
  const [sessionStats, setSessionStats] = useState({
    handsPlayed: 0,
    correctDecisions: 0,
    totalTime: 0,
    currentStreak: 0
  })
  const [isPlaying, setIsPlaying] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState({
    category: 'all',
    difficulty: 'all',
    timeLimit: 30,
    showHints: true
  })
  const [feedback, setFeedback] = useState<{
    show: boolean
    isCorrect: boolean
    message: string
  }>({ show: false, isCorrect: false, message: '' })

  useEffect(() => {
    // Criar usuário mock para teste
    const mockUser = {
      id: 'mock-user',
      username: 'test-user',
      language: 'pt'
    }
    
    setUser(mockUser)
    
    // Verificar se há parâmetros de categoria na URL
    const urlParams = new URLSearchParams(window.location.search)
    const categoryParam = urlParams.get('category')
    
    if (categoryParam) {
      const newSettings = {
        ...settings,
        category: categoryParam
      }
      setSettings(newSettings)
      // Passar as configurações diretamente para garantir que sejam aplicadas
      loadNextScenario(newSettings)
    } else {
      loadNextScenario()
    }
  }, [router])



  const loadNextScenario = async (customSettings?: any) => {
    try {
      setIsLoading(true)
      
      // Usar configurações customizadas se fornecidas, senão usar as do estado
      const currentSettings = customSettings || settings
      console.log('Carregando próximo cenário com filtros:', currentSettings)
      
      let attempts = 0
      let newScenario = null
      
      // Tentar até 5 vezes para evitar repetições
      while (attempts < 5) {
        // Construir URL com filtros - buscar mais cenários para ter opções
        const params = new URLSearchParams()
        if (currentSettings.category !== 'all') params.append('category', currentSettings.category)
        if (currentSettings.difficulty !== 'all') params.append('difficulty', currentSettings.difficulty)
        params.append('count', '5') // Buscar 5 cenários para ter opções
        
        const url = params.toString() 
          ? `/api/scenarios/filtered?${params.toString()}`
          : '/api/scenarios/demo'
        
        const response = await fetch(url)
        const data = await response.json()
        
        if (data.success && data.scenarios && data.scenarios.length > 0) {
          // Filtrar cenários que não estão na lista dos últimos mostrados
          const availableScenarios = data.scenarios.filter(
            (scenario: any) => !lastScenarioIds.includes(scenario.id)
          )
          
          if (availableScenarios.length > 0) {
            // Pegar um cenário aleatório dos disponíveis
            newScenario = availableScenarios[Math.floor(Math.random() * availableScenarios.length)]
            break
          } else if (data.scenarios.length > 0) {
            // Se todos os cenários já foram mostrados, pegar qualquer um
            newScenario = data.scenarios[0]
            break
          }
        }
        
        attempts++
      }
      
      if (newScenario) {
        console.log('Cenário carregado:', newScenario.name)
        setCurrentScenario(newScenario)
        
        // Atualizar lista dos últimos cenários (manter apenas os últimos 10)
        setLastScenarioIds(prev => {
          const updated = [newScenario.id, ...prev].slice(0, 10)
          return updated
        })
      } else {
        console.error('Nenhum cenário retornado pela API')
        setCurrentScenario(null)
      }
    } catch (error) {
      console.error('Erro ao carregar cenário:', error)
      setCurrentScenario(null)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDecision = async (action: PokerAction) => {
    if (!currentScenario || !user) return

    // Extrair a ação correta do cenário
    const correctAction = currentScenario.recommendation?.action?.type || currentScenario.correctAction
    const userActionType = typeof action === 'string' ? action : action.type
    
    const isCorrect = userActionType === correctAction
    
    console.log('Decisão do usuário:', userActionType)
    console.log('Ação correta:', correctAction)
    console.log('Está correto:', isCorrect)
    
    // Atualizar estatísticas da sessão
    setSessionStats(prev => ({
      ...prev,
      handsPlayed: prev.handsPlayed + 1,
      correctDecisions: prev.correctDecisions + (isCorrect ? 1 : 0),
      currentStreak: isCorrect ? prev.currentStreak + 1 : 0
    }))

    // Mostrar feedback visual
    const feedbackMessage = isCorrect 
      ? `✅ Correto! ${currentScenario.recommendation?.explanation || 'Boa jogada!'}`
      : `❌ Incorreto. A ação correta seria: ${correctAction}. ${currentScenario.recommendation?.explanation || ''}`
    
    setFeedback({
      show: true,
      isCorrect,
      message: feedbackMessage
    })

    // Esconder feedback após 3 segundos
    setTimeout(() => {
      setFeedback({ show: false, isCorrect: false, message: '' })
    }, 3000)

    // Enviar resultado para API (opcional - pode falhar sem problemas)
    try {
      await fetch('/api/training/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scenarioId: currentScenario.id,
          userAction: userActionType,
          isCorrect,
          timeSpent: 15
        })
      })
    } catch (error) {
      console.log('API de sessão não disponível (normal em desenvolvimento)')
    }

    // Carregar próximo cenário após um delay
    setTimeout(() => {
      loadNextScenario()
    }, 3000)
  }

  const resetSession = () => {
    setSessionStats({
      handsPlayed: 0,
      correctDecisions: 0,
      totalTime: 0,
      currentStreak: 0
    })
    loadNextScenario()
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/dashboard/training" 
                className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
{t('training.backToTraining')}
              </Link>
              <div className="w-px h-6 bg-gray-600"></div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  {t('training.trainingSimulation')}
                </h1>
                <p className="text-gray-400 text-sm">
                  {t('training.practiceRealScenarios')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 rounded-lg bg-gray-700 text-gray-300 hover:text-white hover:bg-gray-600 transition-colors"
              >
                <Settings className="w-5 h-5" />
              </button>
              
              <button
                onClick={resetSession}
                className="p-2 rounded-lg bg-gray-700 text-gray-300 hover:text-white hover:bg-gray-600 transition-colors"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Toast */}
      {feedback.show && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`fixed top-20 left-1/2 transform -translate-x-1/2 z-50 p-4 rounded-lg shadow-lg max-w-md ${
            feedback.isCorrect 
              ? 'bg-green-600 border border-green-500' 
              : 'bg-red-600 border border-red-500'
          }`}
        >
          <p className="text-white font-medium">{feedback.message}</p>
        </motion.div>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Área Principal - Mesa de Poker */}
          <div className="lg:col-span-3">
            {isLoading ? (
              <div className="bg-gray-800 rounded-xl border border-gray-700 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Carregando cenário...</p>
                </div>
              </div>
            ) : currentScenario ? (
              <div>
                <div className="mb-4 p-4 bg-gray-700 rounded-lg">
                  <h3 className="text-white font-bold">{currentScenario.name}</h3>
                  <p className="text-gray-300 text-sm">{currentScenario.description}</p>
                  <p className="text-gray-400 text-xs mt-2">
                    Street: {currentScenario.gameState?.street} | 
                    Position: {currentScenario.gameState?.position?.name} | 
                    Pot: ${currentScenario.gameState?.pot}
                  </p>
                </div>
                <PokerTable 
                  gameState={currentScenario?.gameState}
                  onAction={handleDecision}
                  showRecommendation={false}
                  availableActions={currentScenario?.availableActions}
                />
              </div>
            ) : (
              <div className="bg-gray-800 rounded-xl border border-gray-700 p-8 text-center">
                <p className="text-gray-400">Nenhum cenário disponível</p>
                <button 
                  onClick={loadNextScenario}
                  className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                >
                  Tentar Carregar Cenário
                </button>
              </div>
            )}
          </div>

          {/* Sidebar - Estatísticas e Controles */}
          <div className="space-y-6">
            {/* Estatísticas da Sessão */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">{t('training.sessionStats')}</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-300">{t('training.handsPlayed')}</span>
                  </div>
                  <span className="text-white font-bold">{sessionStats.handsPlayed}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">{t('training.precision')}</span>
                  </div>
                  <span className="text-white font-bold">
                    {sessionStats.handsPlayed > 0 
                      ? Math.round((sessionStats.correctDecisions / sessionStats.handsPlayed) * 100)
                      : 0}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-yellow-400" />
                    <span className="text-gray-300">{t('training.sequence')}</span>
                  </div>
                  <span className="text-white font-bold">{sessionStats.currentStreak}</span>
                </div>
              </div>
            </motion.div>

            {/* Configurações */}
            {showSettings && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-gray-800 rounded-xl border border-gray-700 p-6"
              >
                <h3 className="text-lg font-bold text-white mb-4">Configurações</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Categoria
                    </label>
                    <select
                      value={settings.category}
                      onChange={(e) => {
                        setSettings(prev => ({ ...prev, category: e.target.value }))
                        // Recarregar cenário com novo filtro
                        setTimeout(() => loadNextScenario(), 100)
                      }}
                      className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Todas</option>
                      <option value="preflop">Preflop (67)</option>
                      <option value="3bet">3-bet (29)</option>
                      <option value="cbet">C-bet (29)</option>
                      <option value="turn_aggression">Turn Aggression (41)</option>
                      <option value="river_value">River Value (24)</option>
                      <option value="donk_lead">Donk Lead (22)</option>
                      <option value="river_aggression">River Aggression (20)</option>
                      <option value="turn_play">Turn Play (20)</option>
                      <option value="river_bluff">River Bluff (18)</option>
                      <option value="float">Float (18)</option>
                      <option value="check_raise">Check Raise (14)</option>
                      <option value="balance">Balance (10)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Dificuldade
                    </label>
                    <select
                      value={settings.difficulty}
                      onChange={(e) => {
                        setSettings(prev => ({ ...prev, difficulty: e.target.value }))
                        // Recarregar cenário com novo filtro
                        setTimeout(() => loadNextScenario(), 100)
                      }}
                      className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Todas</option>
                      <option value="beginner">Iniciante</option>
                      <option value="intermediate">Intermediário</option>
                      <option value="advanced">Avançado</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-300">Mostrar Dicas</span>
                    <button
                      onClick={() => setSettings(prev => ({ ...prev, showHints: !prev.showHints }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.showHints ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          settings.showHints ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Controles */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">Controles</h3>
              
              <div className="space-y-3">
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  <span>{isPlaying ? 'Pausar' : 'Continuar'}</span>
                </button>
                
                <button
                  onClick={loadNextScenario}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Próximo Cenário
                </button>
                
                <button
                  onClick={resetSession}
                  className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Reiniciar Sessão
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
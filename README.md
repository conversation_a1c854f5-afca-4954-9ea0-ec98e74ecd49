# GTO Poker Trainer

Uma aplicação web completa e gamificada para treinar jogadores de poker online (cash games e torneios) no estilo GTO (Game Theory Optimal). A ferramenta guia o usuário do pré-flop ao river, oferecendo exercícios, quizzes e simulações real-time de mãos e situações específicas.

## 🚀 Funcionalidades Principais

### 🎯 Treinamento Completo
- **Pré-flop ao River**: Exercícios cobrindo todas as fases da mão
- **Cenários Variados**: 3-bet, C-bet, check-raise, value betting, bluffs
- **Simulações Real-time**: Interface interativa inspirada no GTO Wizard

### 🧠 Sugestões GTO Inteligentes
- **Ações Ótimas**: Recomendações baseadas em análise de range e equidade
- **Explicações Didáticas**: Cada sugestão vem com explicação detalhada
- **Tamanhos de Aposta**: Cálculo de bet sizing ideal para cada situação

### 🎮 Sistema Gamificado
- **8 Níveis de Progressão**: Do Novato ao Grão-mestre
- **Sistema de XP**: Ganhe experiência a cada acerto
- **Conquistas**: Desbloqueie medalhas e recompensas
- **Ranking Global**: Compete com outros jogadores

### 📊 Análise Avançada
- **Estatísticas Detalhadas**: Precisão por street, frequências de ação
- **Aprendizado Adaptativo**: Sistema identifica pontos fracos
- **Histórico Completo**: Todas as mãos são salvas para análise

### 🌐 Interface Moderna
- **Responsiva**: Funciona em desktop, tablet e mobile
- **Bilingue**: Português e Inglês
- **Animações Fluidas**: Transições suaves e efeitos visuais
- **Mesa Realista**: Interface baseada em mesas reais de poker

## 🛠️ Tecnologias Utilizadas

### Frontend
- **Next.js 14** - Framework React com App Router
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Estilização utilitária
- **Framer Motion** - Animações e transições
- **Lucide React** - Ícones modernos

### Backend
- **Next.js API Routes** - API serverless
- **Prisma ORM** - Gerenciamento de banco de dados
- **JWT** - Autenticação segura
- **bcryptjs** - Hash de senhas

### Banco de Dados
- **MySQL** - Banco relacional
- **Prisma Client** - Type-safe database client

### Hospedagem
- **Vercel** - Deploy serverless
- **PlanetScale/Railway** - Banco de dados em nuvem

## 📦 Instalação e Configuração

### Pré-requisitos
- Node.js 18+ 
- MySQL (XAMPP para desenvolvimento local)
- npm ou yarn

### 1. Clone o repositório
```bash
git clone https://github.com/seu-usuario/gto-poker-trainer.git
cd gto-poker-trainer
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure o banco de dados
```bash
# Inicie o XAMPP e crie um banco chamado 'gto_poker_trainer'
# Copie o arquivo de ambiente
cp .env.example .env

# Edite o .env com suas configurações
DATABASE_URL="mysql://root:@localhost:3306/gto_poker_trainer"
NEXTAUTH_SECRET="seu-secret-muito-seguro-aqui"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Configure o Prisma
```bash
# Gere o cliente Prisma
npx prisma generate

# Execute as migrações
npx prisma db push

# Popule o banco com dados iniciais
npx prisma db seed
```

### 5. Inicie o servidor de desenvolvimento
```bash
npm run dev
```

A aplicação estará disponível em `http://localhost:3000`

## 🎮 Como Usar

### 1. Registro e Login
- Acesse a página inicial
- Clique em "Cadastrar" para criar uma conta
- Faça login com suas credenciais

### 2. Dashboard
- Visualize seu progresso (nível, XP, estatísticas)
- Escolha entre diferentes modos de treinamento
- Acompanhe suas conquistas

### 3. Modos de Treinamento

#### Quiz Rápido (5-10 min)
- 10 questões aleatórias
- Tempo limitado por questão
- Feedback imediato

#### Simulação Completa (15-30 min)
- Mãos completas do pré-flop ao river
- Análise detalhada de cada decisão
- Cenários realistas

#### Prática Livre (Ilimitado)
- Treine sem pressão de tempo
- Explore diferentes situações
- Foque em áreas específicas

### 4. Análise e Progresso
- Acesse suas estatísticas detalhadas
- Veja o ranking global
- Acompanhe seu desenvolvimento

## 🏗️ Estrutura do Projeto

```
src/
├── app/                    # App Router (Next.js 14)
│   ├── api/               # API Routes
│   ├── auth/              # Páginas de autenticação
│   ├── dashboard/         # Dashboard principal
│   ├── training/          # Páginas de treinamento
│   └── globals.css        # Estilos globais
├── components/            # Componentes React
│   ├── poker/            # Componentes específicos de poker
│   ├── ui/               # Componentes de interface
│   └── providers/        # Context providers
├── contexts/             # React Contexts
├── hooks/                # Custom hooks
├── lib/                  # Utilitários e configurações
├── types/                # Definições TypeScript
└── utils/                # Funções utilitárias

prisma/
├── schema.prisma         # Schema do banco de dados
└── seed.ts              # Script de população inicial
```

## 🎯 Cenários de Treinamento

### Pré-flop
- Ranges de abertura por posição
- 3-bet e 4-bet ranges
- Defesa de blinds
- Squeeze plays

### Pós-flop
- Continuation betting
- Check-raising
- Barrel bluffing
- Pot control
- River decisions

### Situações Especiais
- All-in decisions
- ICM considerations (torneios)
- Short stack play
- Deep stack adjustments

## 📊 Sistema de Níveis

1. **Novato** (0 XP) - Primeiros passos
2. **Iniciante** (100 XP) - Conceitos básicos
3. **Amador** (300 XP) - Fundamentos sólidos
4. **Intermediário** (600 XP) - Estratégias avançadas
5. **Avançado** (1000 XP) - Domínio técnico
6. **Especialista** (1500 XP) - Expertise comprovada
7. **Mestre** (2100 XP) - Conhecimento profundo
8. **Grão-mestre** (2800 XP) - Elite do poker

## 🏆 Sistema de Conquistas

- **Primeiros Passos**: Complete sua primeira sessão
- **Mestre da Precisão**: 90% de precisão em 10 sessões
- **Guerreiro da Sequência**: 20 acertos consecutivos
- **Jogador Avançado**: Alcance o nível 5
- E muitas outras...

## 🚀 Deploy na Vercel

### 1. Conecte seu repositório
- Faça push do código para GitHub
- Conecte o repositório na Vercel

### 2. Configure as variáveis de ambiente
```bash
DATABASE_URL="sua-url-do-banco-em-producao"
NEXTAUTH_SECRET="seu-secret-de-producao"
NEXTAUTH_URL="https://seu-dominio.vercel.app"
```

### 3. Configure o banco de dados
- Use PlanetScale, Railway ou outro provedor MySQL
- Execute as migrações em produção
- Popule com dados iniciais

## 🧪 Testes

```bash
# Executar testes
npm test

# Testes em modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🎯 Roadmap Futuro

- [ ] Integração com solvers GTO reais (PioSolver, GTO+)
- [ ] Modo multiplayer para treino em grupo
- [ ] Análise de mãos importadas
- [ ] Mobile app nativo
- [ ] Integração com sites de poker
- [ ] Coaching personalizado com IA
- [ ] Torneios internos
- [ ] Estatísticas avançadas com Machine Learning

## 📞 Suporte

Para dúvidas, sugestões ou reportar bugs:
- Abra uma issue no GitHub
- Entre em contato: <EMAIL>

---

**Desenvolvido com ❤️ para a comunidade de poker**
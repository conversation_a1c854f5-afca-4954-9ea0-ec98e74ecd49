import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      sessionType,
      difficulty,
      score,
      totalQuestions,
      correctAnswers,
      xpGained,
      duration,
      completed
    } = await request.json()

    // Criar sessão de treinamento
    const session = await prisma.trainingSession.create({
      data: {
        userId,
        sessionType,
        difficulty,
        score,
        totalQuestions,
        correctAnswers,
        xpGained,
        duration,
        completed
      }
    })

    // Atualizar XP e pontos do usuário
    await prisma.user.update({
      where: { id: userId },
      data: {
        xp: { increment: xpGained },
        totalPoints: { increment: score * 10 }
      }
    })

    // Atualizar estatísticas do usuário
    const userStats = await prisma.userStatistics.findUnique({
      where: { userId }
    })

    if (userStats) {
      const accuracy = correctAnswers / totalQuestions
      const newTotalHands = userStats.totalHands + totalQuestions
      const newCorrectDecisions = userStats.correctDecisions + correctAnswers
      const newAverageEquity = (userStats.averageEquity * userStats.totalHands + accuracy * totalQuestions) / newTotalHands

      // Atualizar streak
      let newCurrentStreak = userStats.currentStreak
      let newLongestStreak = userStats.longestStreak

      if (accuracy >= 0.8) { // 80% de acerto mantém a streak
        newCurrentStreak += 1
        newLongestStreak = Math.max(newLongestStreak, newCurrentStreak)
      } else {
        newCurrentStreak = 0
      }

      await prisma.userStatistics.update({
        where: { userId },
        data: {
          totalHands: newTotalHands,
          correctDecisions: newCorrectDecisions,
          averageEquity: newAverageEquity,
          currentStreak: newCurrentStreak,
          longestStreak: newLongestStreak,
          // Atualizar precisão por street (simplificado)
          preflopAccuracy: accuracy,
          flopAccuracy: accuracy,
          turnAccuracy: accuracy,
          riverAccuracy: accuracy
        }
      })
    }

    // Verificar conquistas
    await checkAchievements(userId)

    return NextResponse.json({
      success: true,
      session,
      xpGained
    })

  } catch (error) {
    console.error('Erro ao salvar sessão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

async function checkAchievements(userId: string) {
  try {
    // Buscar estatísticas atualizadas
    const userStats = await prisma.userStatistics.findUnique({
      where: { userId }
    })

    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!userStats || !user) return

    // Verificar conquista de primeira sessão
    const existingFirstSession = await prisma.userAchievement.findFirst({
      where: {
        userId,
        achievement: {
          name: 'first_session'
        }
      }
    })

    if (!existingFirstSession && userStats.totalHands >= 1) {
      const achievement = await prisma.achievement.findUnique({
        where: { name: 'first_session' }
      })

      if (achievement) {
        await prisma.userAchievement.create({
          data: {
            userId,
            achievementId: achievement.id
          }
        })

        // Dar XP da conquista
        await prisma.user.update({
          where: { id: userId },
          data: {
            xp: { increment: achievement.xpReward }
          }
        })
      }
    }

    // Verificar conquista de streak
    if (userStats.currentStreak >= 20) {
      const existingStreak = await prisma.userAchievement.findFirst({
        where: {
          userId,
          achievement: {
            name: 'streak_warrior'
          }
        }
      })

      if (!existingStreak) {
        const achievement = await prisma.achievement.findUnique({
          where: { name: 'streak_warrior' }
        })

        if (achievement) {
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id
            }
          })

          await prisma.user.update({
            where: { id: userId },
            data: {
              xp: { increment: achievement.xpReward }
            }
          })
        }
      }
    }

    // Verificar conquista de nível
    if (user.level >= 5) {
      const existingLevel = await prisma.userAchievement.findFirst({
        where: {
          userId,
          achievement: {
            name: 'level_5'
          }
        }
      })

      if (!existingLevel) {
        const achievement = await prisma.achievement.findUnique({
          where: { name: 'level_5' }
        })

        if (achievement) {
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id
            }
          })

          await prisma.user.update({
            where: { id: userId },
            data: {
              xp: { increment: achievement.xpReward }
            }
          })
        }
      }
    }

  } catch (error) {
    console.error('Erro ao verificar conquistas:', error)
  }
}
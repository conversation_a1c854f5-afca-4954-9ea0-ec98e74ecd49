export const translations = {
  pt: {
    // Navegação e UI geral
    nav: {
      home: 'Início',
      training: 'Treinamento',
      statistics: 'Estatísticas',
      achievements: 'Conquistas',
      profile: 'Perfil',
      leaderboard: 'Ranking',
      settings: 'Configurações'
    },

    // Autenticação
    auth: {
      login: 'Entrar',
      register: '<PERSON><PERSON>stra<PERSON>',
      logout: 'Sair',
      email: 'E-mail',
      password: '<PERSON><PERSON>',
      username: 'Nome de usuário',
      confirmPassword: 'Confirmar senha',
      forgotPassword: '<PERSON>squeci minha senha',
      loginTitle: 'Entre na sua conta',
      registerTitle: '<PERSON><PERSON> sua conta',
      loginButton: 'Entrar',
      registerButton: '<PERSON><PERSON>stra<PERSON>',
      alreadyHaveAccount: 'Já tem uma conta?',
      dontHaveAccount: 'Não tem uma conta?',
      clickHere: 'Clique aqui',
      emailPlaceholder: '<EMAIL>',
      passwordPlaceholder: '••••••••',
      loggingIn: 'Entrando...',
      usernamePlaceholder: 'seuusuario',
      creatingAccount: '<PERSON><PERSON><PERSON> conta...'
    },

    // Página inicial
    hero: {
      title: '<PERSON><PERSON> o Poker GTO',
      subtitle: 'Treine estratégias Game Theory Optimal com exercícios interativos, simulações em tempo real e análises detalhadas.',
      startTraining: 'Começar Treinamento',
      tryDemo: 'Experimentar Demo'
    },

    // Estatísticas da página inicial
    stats: {
      scenarios: 'Cenários de Treino',
      accuracy: 'Precisão Média',
      available: 'Disponível'
    },

    // Funcionalidades
    features: {
      title: 'Funcionalidades Principais',
      subtitle: 'Tudo que você precisa para dominar o poker GTO',
      realTimeTraining: {
        title: 'Treino em Tempo Real',
        desc: 'Simulações interativas com feedback instantâneo e explicações detalhadas'
      },
      gamification: {
        title: 'Sistema Gamificado',
        desc: 'Níveis, conquistas e ranking para manter você motivado'
      },
      analytics: {
        title: 'Análises Avançadas',
        desc: 'Estatísticas detalhadas e identificação de pontos fracos'
      },
      adaptive: {
        title: 'Aprendizado Adaptativo',
        desc: 'O sistema se adapta ao seu nível e foca nas suas necessidades'
      }
    },

    // Call to Action
    cta: {
      title: 'Pronto para Elevar seu Jogo?',
      subtitle: 'Junte-se a milhares de jogadores que já melhoraram com nosso treinamento GTO',
      button: 'Começar Agora Grátis'
    },

    // Footer
    footer: {
      rights: 'Todos os direitos reservados.'
    },

    // Dashboard
    dashboard: {
      welcome: 'Bem-vindo',
      readyToTrain: 'Pronto para treinar hoje?',
      recentActivity: 'Atividade Recente',
      viewAll: 'Ver todas',
      upgradeProTitle: 'Upgrade Pro',
      unlockAdvancedAnalytics: 'Desbloqueie análises avançadas e cenários ilimitados',
      viewPlans: 'Ver Planos',
      level: 'Nível',
      logout: 'Sair',
      chooseTraining: 'Escolha seu Treinamento',
      welcomeMessage: 'Bem-vindo ao GTO Trainer!',
      startFirstTraining: 'Comece seu primeiro treinamento para ver suas atividades aqui',
      xpToNextLevel: 'XP para o próximo nível',
      maxLevelReached: 'Nível máximo alcançado!',
      totalXP: 'XP Total',
      quickTest: 'Teste rápido com 10 questões',
      fullSimulation: 'Simulação completa de mãos',
      freePractice: 'Prática livre sem limite',
      unlimited: 'Ilimitado',
      start: 'Iniciar'
    },

    // Treinamento
    training: {
      title: 'Área de Treinamento',
      selectMode: 'Selecione o Modo de Treino',
      quiz: 'Quiz Rápido',
      simulation: 'Simulação Completa',
      practice: 'Prática Livre',
      difficulty: 'Dificuldade',
      beginner: 'Iniciante',
      intermediate: 'Intermediário',
      advanced: 'Avançado',
      start: 'Iniciar',
      continue: 'Continuar',
      pause: 'Pausar',
      finish: 'Finalizar',
      backToDashboard: 'Voltar ao Dashboard',
      backToTraining: 'Voltar ao Treinamento',
      sessionStats: 'Estatísticas da Sessão',
      handsPlayed: 'Mãos Jogadas',
      precision: 'Precisão',
      currentStreak: 'Sequência Atual',
      sequence: 'Sequência',
      yourProgress: 'Seu Progresso',
      continueTraining: 'Continue treinando para melhorar suas estatísticas',
      viewStatistics: 'Ver Estatísticas',
      recentActivity: 'Atividade Recente',
      viewAll: 'Ver todas',
      trainingSimulation: 'Simulação de Treinamento',
      practiceRealScenarios: 'Pratique cenários reais de poker',
      loadingScenario: 'Carregando cenário...',
      noScenarioAvailable: 'Nenhum cenário disponível',
      category: 'Categoria',
      all: 'Todas',
      preflop: 'Pré-flop',
      postflop: 'Pós-flop',
      bluffing: 'Bluff',
      showHints: 'Mostrar Dicas',
      controls: 'Controles',
      nextScenario: 'Próximo Cenário',
      restartSession: 'Reiniciar Sessão',
      detailedStatistics: 'Estatísticas Detalhadas',
      profileInfo: 'Informações do Perfil',
      recentAchievements: 'Conquistas Recentes',
      noAchievementsYet: 'Nenhuma conquista ainda',
      keepTrainingToUnlock: 'Continue treinando para desbloquear conquistas!',
      myAchievements: 'Minhas Conquistas',
      allCategories: 'Todas',
      allDifficulties: 'Todas',
      trainingCenter: 'Centro de Treinamento',
      chooseTrainingMode: 'Escolha seu modo de treinamento e melhore suas habilidades no poker',
      trainingModes: 'Modos de Treinamento',
      trainingCategories: 'Categorias de Treinamento',
      searchCategory: 'Buscar categoria...',
      scenarios: 'cenários',
      continueTrainingImprove: 'Continue treinando para melhorar suas estatísticas',
      categories: {
        cbet_spots: 'C-Bet Spots',
        '3bet_defense': '3-Bet Defense',
        bluff_catching: 'Bluff Catching',
        value_betting: 'Value Betting'
      },
      categoryDescriptions: {
        cbet_spots: 'Aprenda quando e como fazer continuation bets',
        '3bet_defense': 'Estratégias para defender contra 3-bets',
        bluff_catching: 'Identifique e capture bluffs do oponente',
        value_betting: 'Maximize valor com suas mãos fortes'
      }
    },

    // Ações de poker
    actions: {
      fold: 'Desistir',
      call: 'Pagar',
      pendente: 'Pendente',
      raise: 'Aumentar',
      check: 'Check',
      bet: 'Apostar',
      allin: 'All-in'
    },

    // Posições
    positions: {
      UTG: 'UTG',
      MP: 'MP',
      CO: 'CO',
      BTN: 'BTN',
      SB: 'SB',
      BB: 'BB'
    },

    // Streets
    streets: {
      preflop: 'Pré-flop',
      flop: 'Flop',
      turn: 'Turn',
      river: 'River'
    },

    // Resultados
    results: {
      correct: 'Correto!',
      incorrect: 'Incorreto',
      explanation: 'Explicação',
      yourAnswer: 'Sua resposta',
      correctAnswer: 'Resposta correta',
      nextQuestion: 'Próxima pergunta',
      sessionComplete: 'Sessão completa!',
      score: 'Pontuação',
      accuracy: 'Precisão',
      xpGained: 'XP ganho'
    },

    // Estatísticas
    statistics: {
      title: 'Suas Estatísticas',
      overview: 'Visão Geral',
      totalHands: 'Total de Mãos',
      winRate: 'Taxa de Vitória',
      averageEquity: 'Equidade Média',
      preflopAccuracy: 'Precisão Pré-flop',
      postflopAccuracy: 'Precisão Pós-flop',
      aggressionFreq: 'Frequência de Agressão',
      currentStreak: 'Sequência Atual',
      longestStreak: 'Maior Sequência',
      handsPlayed: 'Mãos Jogadas',
      overallAccuracy: 'Precisão Geral',
      correctDecisions: 'corretas',
      noHandsPlayed: 'Nenhuma mão jogada',
      noDecisionsYet: 'Nenhuma decisão ainda',
      record: 'Recorde',
      hits: 'acertos',
      inPlayedHands: 'Nas mãos jogadas',
      accuracyByStreet: 'Precisão por Street',
      actionFrequency: 'Frequência de Ações',
      decisionsOf: 'de',
      decisions: 'decisões',
      errorLoadingStats: 'Erro ao carregar estatísticas',
      dataNotAvailable: 'Dados não disponíveis',
      notAuthenticated: 'Não autenticado',
      errorFetchingStats: 'Erro ao buscar estatísticas',
      detailedStatistics: 'Estatísticas Detalhadas',
      completeAnalysis: 'Análise completa do seu desempenho no treinamento GTO',
      lastWeek: 'Última semana',
      lastMonth: 'Último mês',
      allTime: 'Todo período',
      export: 'Exportar',
      level: 'Nível',
      memberSince: 'Membro desde',
      totalXP: 'XP Total',
      trendAnalysis: 'Análise de Tendências',
      strengths: 'Pontos Fortes',
      areasForImprovement: 'Áreas de Melhoria',
      recommendations: 'Recomendações',
      globalComparison: 'Comparação Global',
      yourRanking: 'Sua Posição no Ranking',
      communityAverages: 'Médias da Comunidade',
      maxStreak: 'Sequência Máxima',
      averageXP: 'XP Médio',
      handsPerSession: 'Mãos por Sessão',
      strengthsItems: [
        'Boa precisão no pré-flop',
        'Frequência de C-bet adequada',
        'Sequência consistente de acertos'
      ],
      improvementItems: [
        'Decisões no river',
        'Spots de check-raise',
        'Bluff catching'
      ],
      recommendationItems: [
        'Foque em cenários de river',
        'Pratique mais spots avançados',
        'Revise teoria de bluff catch'
      ]
    },

    // Perfil
    profile: {
      title: 'Perfil',
      level: 'Nível',
      xp: 'Experiência',
      totalPoints: 'Pontos Totais',
      achievements: 'Conquistas',
      recentActivity: 'Atividade Recente',
      settings: 'Configurações',
      language: 'Idioma',
      notifications: 'Notificações',
      profileInfo: 'Informações do Perfil',
      edit: 'Editar',
      save: 'Salvar',
      saving: 'Salvando...',
      cancel: 'Cancelar',
      username: 'Nome de usuário',
      email: 'Email',
      memberSince: 'Membro desde',
      detailedStats: 'Estatísticas Detalhadas',
      handsPlayed: 'Mãos Jogadas',
      accuracy: 'Precisão',
      bestStreak: 'Melhor Sequência',
      averageTime: 'Tempo Médio',
      recentAchievements: 'Conquistas Recentes',
      noAchievementsYet: 'Nenhuma conquista ainda',
      keepTrainingUnlock: 'Continue treinando para desbloquear conquistas!',
      levelProgress: 'Progresso de Nível',
      xpToNextLevel: 'XP para o próximo nível',
      favoriteCategory: 'Categoria Favorita',
      mostPracticed: 'Mais praticada',
      quickLinks: 'Links Rápidos',
      viewStats: 'Ver Estatísticas',
      myAchievements: 'Minhas Conquistas',
      globalRanking: 'Ranking Global'
    },

    // Conquistas
    achievements: {
      title: 'Conquistas',
      progress: 'Progresso',
      unlocked: 'Desbloqueada!',
      loadingAchievements: 'Carregando conquistas...',
      achievementsProgress: 'Progresso das Conquistas',
      continueTrainingUnlock: 'Continue treinando para desbloquear mais conquistas!',
      complete: 'Completo',
      all: 'Todas',
      unlockedFilter: 'Desbloqueadas',
      locked: 'Bloqueadas',
      allCategories: 'Todas as categorias',
      searchAchievements: 'Buscar conquistas...',
      noAchievementsFound: 'Nenhuma conquista encontrada',
      adjustFilters: 'Tente ajustar os filtros ou continue treinando para desbloquear mais conquistas!',
      unlockedOf: 'de',
      achievementsUnlocked: 'conquistas desbloqueadas',
      categories: {
        training: 'Treinamento',
        accuracy: 'Precisão',
        streak: 'Sequência',
        level: 'Nível'
      }
    },

    // Mesa de poker
    poker: {
      pot: 'Pote',
      street: 'Street',
      position: 'Posição',
      stack: 'Stack',
      yourTurn: 'SUA VEZ',
      yourChoice: 'Sua Escolha',
      gtoRecommended: 'Ação GTO Recomendada',
      excellentPlay: 'Excelente Jogada! 🎉',
      youChoseGto: 'Você escolheu a ação GTO recomendada',
      actionNotRecommended: 'Ação Não Recomendada ❌',
      youChose: 'Você escolheu',
      butGtoRecommends: 'mas a ação GTO recomendada é',
      frequency: 'Frequência',
      equity: 'Equidade',
      whyCorrectPlay: 'Por que esta é a jogada correta:',
      analysisYourDecision: 'Análise da sua decisão:',
      analysisYourChoice: '⚠️ Análise da sua escolha:',
      yourAction: 'Sua Ação',
      recommendedAction: 'Ação Recomendada',
      gtoFrequency: 'Frequência GTO',
      excellentDecision: '✅ Excelente decisão!',
      xpGained: 'XP Ganho',
      newSituation: 'Nova Situação',
      tryAgain: 'Tentar Novamente',
      tableActions: 'Ações na Mesa',
      you: 'Você',
      yourTurnToAct: 'Sua vez de agir',
      actionRequired: 'AÇÃO NECESSÁRIA',
      situationContext: 'Contexto da Situação',
      situation: 'Situação',
      cashGame: 'Cash Game',
      tournament: 'Torneio',
      players: 'Jogadores',
      active: 'ativos',
      ofTable: 'de 6 na mesa',
      potOdds: 'Pot Odds',
      invested: 'investido',
      aggression: 'Agressão',
      present: 'Presente',
      absent: 'Ausente',
      passiveTable: 'Mesa passiva',
      gtoTip: '💡 Dica GTO:',
      tips: {
        latePosition: 'Em posição tardia, você pode abrir com um range mais amplo e aplicar pressão.',
        earlyPosition: 'Em posição inicial, seja mais seletivo com suas mãos de abertura.',
        blinds: 'Nos blinds, considere defender com mãos que têm boa jogabilidade pós-flop.',
        withAggression: 'Com agressão na mesa, considere suas odds de pot e a força da sua mão para continuar.',
        fewPlayers: 'Com poucos jogadores, você pode ser mais agressivo e blefar com mais frequência.',
        analyzeBoard: 'Analise a textura do board e a força relativa da sua mão nesta situação.'
      }
    },

    // Demo
    demo: {
      title: 'Demo - GTO Trainer',
      subtitle: 'Experimente nossa interface de treinamento',
      back: 'Voltar',
      createAccount: 'Criar Conta',
      howItWorks: 'Como Funciona',
      howItWorksDescription: 'Esta é uma demonstração da nossa interface de treinamento. Você está vendo uma situação real de poker onde precisa tomar uma decisão. Clique em uma das ações abaixo para ver a recomendação GTO.',
      instructions: [
        'Analise suas cartas e a situação na mesa',
        'Considere sua posição e o tamanho do pote',
        'Escolha a ação que considera mais apropriada',
        'Receba feedback detalhado sobre sua decisão'
      ],
      demoScenario: 'Cenário Demo',
      scenarioOf: 'Cenário',
      of: 'de',
      previous: '← Anterior',
      next: 'Próximo →',
      loadingDemo: 'Carregando situação demo...',
      errorLoadingDemo: 'Erro ao carregar situação demo',
      unlockMore: 'Desbloqueie Mais!',
      wantMoreSituations: 'Quer Ver Mais Situações?',
      createFreeAccount: 'Crie sua conta gratuita e acesse:',
      features: [
        'Centenas de situações GTO reais',
        'Sistema de progresso e níveis',
        'Análise detalhada do seu jogo',
        'Conquistas e ranking global'
      ],
      createFreeAccountBtn: '🚀 Criar Conta Grátis',
      alreadyHaveAccount: 'Já tenho conta',
      continueDemo: 'Continuar no demo',
      readyToStart: 'Pronto para Começar seu Treinamento?',
      accessThousands: 'Acesse milhares de cenários, acompanhe seu progresso e melhore suas habilidades no poker.',
      resources: {
        scenarios: '10,000+ Cenários',
        scenariosDesc: 'Treine com milhares de situações reais de poker, desde pré-flop até river.',
        analysis: 'Análise Detalhada',
        analysisDesc: 'Receba explicações completas sobre cada decisão e aprenda a teoria por trás das jogadas.',
        gamified: 'Progresso Gamificado',
        gamifiedDesc: 'Sistema de níveis, conquistas e ranking para manter você motivado a melhorar.'
      }
    },

    // Ranking
    leaderboard: {
      title: 'Ranking',
      rank: 'Posição',
      player: 'Jogador',
      level: 'Nível',
      points: 'Pontos',
      accuracy: 'Precisão'
    },

    // Mensagens de erro
    errors: {
      generic: 'Ocorreu um erro. Tente novamente.',
      network: 'Erro de conexão. Verifique sua internet.',
      invalidCredentials: 'Credenciais inválidas.',
      userExists: 'Usuário já existe.',
      invalidEmail: 'E-mail inválido.',
      passwordTooShort: 'Senha deve ter pelo menos 8 caracteres.',
      passwordsDontMatch: 'Senhas não coincidem.',
      usernameRequired: 'Nome de usuário é obrigatório.',
      emailRequired: 'E-mail é obrigatório.',
      passwordRequired: 'Senha é obrigatória.',
      usernameInvalid: 'Nome de usuário deve ter pelo menos 3 caracteres e conter apenas letras, números e _',
      confirmPasswordRequired: 'Confirmação de senha é obrigatória',
      registering: 'Cadastrando...'
    },

    // Mensagens de sucesso
    success: {
      loginSuccess: 'Login realizado com sucesso!',
      registerSuccess: 'Conta criada com sucesso!',
      profileUpdated: 'Perfil atualizado com sucesso!',
      achievementUnlocked: 'Conquista desbloqueada!'
    },

    // Botões e ações gerais
    buttons: {
      save: 'Salvar',
      cancel: 'Cancelar',
      delete: 'Excluir',
      edit: 'Editar',
      close: 'Fechar',
      back: 'Voltar',
      next: 'Próximo',
      previous: 'Anterior',
      submit: 'Enviar',
      reset: 'Resetar'
    }
  },

  en: {
    // Navigation and general UI
    nav: {
      home: 'Home',
      training: 'Training',
      statistics: 'Statistics',
      achievements: 'Achievements',
      profile: 'Profile',
      leaderboard: 'Leaderboard',
      settings: 'Settings'
    },

    // Authentication
    auth: {
      login: 'Login',
      register: 'Register',
      logout: 'Logout',
      email: 'Email',
      password: 'Password',
      username: 'Username',
      confirmPassword: 'Confirm password',
      forgotPassword: 'Forgot password',
      loginTitle: 'Sign in to your account',
      registerTitle: 'Create your account',
      loginButton: 'Sign In',
      registerButton: 'Register',
      alreadyHaveAccount: 'Already have an account?',
      dontHaveAccount: "Don't have an account?",
      clickHere: 'Click here',
      emailPlaceholder: '<EMAIL>',
      passwordPlaceholder: '••••••••',
      loggingIn: 'Signing in...',
      usernamePlaceholder: 'yourusername',
      creatingAccount: 'Creating account...'
    },

    // Home page
    hero: {
      title: 'Master GTO Poker',
      subtitle: 'Train Game Theory Optimal strategies with interactive exercises, real-time simulations and detailed analysis.',
      startTraining: 'Start Training',
      tryDemo: 'Try Demo'
    },

    // Home page stats
    stats: {
      scenarios: 'Training Scenarios',
      accuracy: 'Average Accuracy',
      available: 'Available'
    },

    // Features
    features: {
      title: 'Key Features',
      subtitle: 'Everything you need to master GTO poker',
      realTimeTraining: {
        title: 'Real-Time Training',
        desc: 'Interactive simulations with instant feedback and detailed explanations'
      },
      gamification: {
        title: 'Gamified System',
        desc: 'Levels, achievements and leaderboards to keep you motivated'
      },
      analytics: {
        title: 'Advanced Analytics',
        desc: 'Detailed statistics and weakness identification'
      },
      adaptive: {
        title: 'Adaptive Learning',
        desc: 'System adapts to your level and focuses on your needs'
      }
    },

    // Call to Action
    cta: {
      title: 'Ready to Elevate Your Game?',
      subtitle: 'Join thousands of players who have already improved with our GTO training',
      button: 'Start Now Free'
    },

    // Footer
    footer: {
      rights: 'All rights reserved.'
    },

    // Dashboard
    dashboard: {
      welcome: 'Welcome',
      readyToTrain: 'Ready to train today?',
      recentActivity: 'Recent Activity',
      viewAll: 'View all',
      upgradeProTitle: 'Upgrade Pro',
      unlockAdvancedAnalytics: 'Unlock advanced analytics and unlimited scenarios',
      viewPlans: 'View Plans',
      level: 'Level',
      logout: 'Logout',
      chooseTraining: 'Choose Your Training',
      welcomeMessage: 'Welcome to GTO Trainer!',
      startFirstTraining: 'Start your first training to see your activities here',
      xpToNextLevel: 'XP to next level',
      maxLevelReached: 'Max level reached!',
      totalXP: 'Total XP',
      quickTest: 'Quick test with 10 questions',
      fullSimulation: 'Full hand simulation',
      freePractice: 'Free practice without limits',
      unlimited: 'Unlimited',
      start: 'Start'
    },

    // Training
    training: {
      title: 'Training Area',
      selectMode: 'Select Training Mode',
      quiz: 'Quick Quiz',
      simulation: 'Full Simulation',
      practice: 'Free Practice',
      difficulty: 'Difficulty',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      start: 'Start',
      continue: 'Continue',
      pause: 'Pause',
      finish: 'Finish',
      backToDashboard: 'Back to Dashboard',
      backToTraining: 'Back to Training',
      sessionStats: 'Session Stats',
      handsPlayed: 'Hands Played',
      precision: 'Precision',
      currentStreak: 'Current Streak',
      sequence: 'Sequence',
      yourProgress: 'Your Progress',
      continueTraining: 'Continue training to improve your statistics',
      viewStatistics: 'View Statistics',
      recentActivity: 'Recent Activity',
      viewAll: 'View all',
      trainingSimulation: 'Training Simulation',
      practiceRealScenarios: 'Practice real poker scenarios',
      loadingScenario: 'Loading scenario...',
      noScenarioAvailable: 'No scenario available',
      category: 'Category',
      all: 'All',
      preflop: 'Preflop',
      postflop: 'Postflop',
      bluffing: 'Bluffing',
      showHints: 'Show Hints',
      controls: 'Controls',
      nextScenario: 'Next Scenario',
      restartSession: 'Restart Session',
      detailedStatistics: 'Detailed Statistics',
      profileInfo: 'Profile Information',
      recentAchievements: 'Recent Achievements',
      noAchievementsYet: 'No achievements yet',
      keepTrainingToUnlock: 'Keep training to unlock achievements!',
      myAchievements: 'My Achievements',
      allCategories: 'All',
      allDifficulties: 'All',
      trainingCenter: 'Training Center',
      chooseTrainingMode: 'Choose your training mode and improve your poker skills',
      trainingModes: 'Training Modes',
      trainingCategories: 'Training Categories',
      searchCategory: 'Search category...',
      scenarios: 'scenarios',
      continueTrainingImprove: 'Continue training to improve your statistics',
      categories: {
        cbet_spots: 'C-Bet Spots',
        '3bet_defense': '3-Bet Defense',
        bluff_catching: 'Bluff Catching',
        value_betting: 'Value Betting'
      },
      categoryDescriptions: {
        cbet_spots: 'Learn when and how to make continuation bets',
        '3bet_defense': 'Strategies to defend against 3-bets',
        bluff_catching: 'Identify and catch opponent bluffs',
        value_betting: 'Maximize value with your strong hands'
      }
    },

    // Poker actions
    actions: {
      fold: 'Fold',
      call: 'Call',
      raise: 'Raise',
      check: 'Check',
      bet: 'Bet',
      allin: 'All-in'
    },

    // Positions
    positions: {
      UTG: 'UTG',
      MP: 'MP',
      CO: 'CO',
      BTN: 'BTN',
      SB: 'SB',
      BB: 'BB'
    },

    // Streets
    streets: {
      preflop: 'Preflop',
      flop: 'Flop',
      turn: 'Turn',
      river: 'River'
    },

    // Results
    results: {
      correct: 'Correct!',
      incorrect: 'Incorrect',
      explanation: 'Explanation',
      yourAnswer: 'Your answer',
      correctAnswer: 'Correct answer',
      nextQuestion: 'Next question',
      sessionComplete: 'Session complete!',
      score: 'Score',
      accuracy: 'Accuracy',
      xpGained: 'XP gained'
    },

    // Statistics
    statistics: {
      title: 'Your Statistics',
      overview: 'Overview',
      totalHands: 'Total Hands',
      winRate: 'Win Rate',
      averageEquity: 'Average Equity',
      preflopAccuracy: 'Preflop Accuracy',
      postflopAccuracy: 'Postflop Accuracy',
      aggressionFreq: 'Aggression Frequency',
      currentStreak: 'Current Streak',
      longestStreak: 'Longest Streak',
      handsPlayed: 'Hands Played',
      overallAccuracy: 'Overall Accuracy',
      correctDecisions: 'correct',
      noHandsPlayed: 'No hands played',
      noDecisionsYet: 'No decisions yet',
      record: 'Record',
      hits: 'hits',
      inPlayedHands: 'In played hands',
      accuracyByStreet: 'Accuracy by Street',
      actionFrequency: 'Action Frequency',
      decisionsOf: 'of',
      decisions: 'decisions',
      errorLoadingStats: 'Error loading statistics',
      dataNotAvailable: 'Data not available',
      notAuthenticated: 'Not authenticated',
      errorFetchingStats: 'Error fetching statistics',
      detailedStatistics: 'Detailed Statistics',
      completeAnalysis: 'Complete analysis of your GTO training performance',
      lastWeek: 'Last week',
      lastMonth: 'Last month',
      allTime: 'All time',
      export: 'Export',
      level: 'Level',
      memberSince: 'Member since',
      totalXP: 'Total XP',
      trendAnalysis: 'Trend Analysis',
      strengths: 'Strengths',
      areasForImprovement: 'Areas for Improvement',
      recommendations: 'Recommendations',
      globalComparison: 'Global Comparison',
      yourRanking: 'Your Ranking Position',
      communityAverages: 'Community Averages',
      maxStreak: 'Max Streak',
      averageXP: 'Average XP',
      handsPerSession: 'Hands per Session',
      strengthsItems: [
        'Good preflop accuracy',
        'Adequate C-bet frequency',
        'Consistent streak of hits'
      ],
      improvementItems: [
        'River decisions',
        'Check-raise spots',
        'Bluff catching'
      ],
      recommendationItems: [
        'Focus on river scenarios',
        'Practice more advanced spots',
        'Review bluff catch theory'
      ]
    },

    // Profile
    profile: {
      title: 'Profile',
      level: 'Level',
      xp: 'Experience',
      totalPoints: 'Total Points',
      achievements: 'Achievements',
      recentActivity: 'Recent Activity',
      settings: 'Settings',
      language: 'Language',
      notifications: 'Notifications',
      profileInfo: 'Profile Information',
      edit: 'Edit',
      save: 'Save',
      saving: 'Saving...',
      cancel: 'Cancel',
      username: 'Username',
      email: 'Email',
      memberSince: 'Member since',
      detailedStats: 'Detailed Statistics',
      handsPlayed: 'Hands Played',
      accuracy: 'Accuracy',
      bestStreak: 'Best Streak',
      averageTime: 'Average Time',
      recentAchievements: 'Recent Achievements',
      noAchievementsYet: 'No achievements yet',
      keepTrainingUnlock: 'Keep training to unlock achievements!',
      levelProgress: 'Level Progress',
      xpToNextLevel: 'XP to next level',
      favoriteCategory: 'Favorite Category',
      mostPracticed: 'Most practiced',
      quickLinks: 'Quick Links',
      viewStats: 'View Statistics',
      myAchievements: 'My Achievements',
      globalRanking: 'Global Ranking'
    },

    // Achievements
    achievements: {
      title: 'Achievements',
      progress: 'Progress',
      unlocked: 'Unlocked!',
      loadingAchievements: 'Loading achievements...',
      achievementsProgress: 'Achievements Progress',
      continueTrainingUnlock: 'Keep training to unlock more achievements!',
      complete: 'Complete',
      all: 'All',
      unlockedFilter: 'Unlocked',
      locked: 'Locked',
      allCategories: 'All categories',
      searchAchievements: 'Search achievements...',
      noAchievementsFound: 'No achievements found',
      adjustFilters: 'Try adjusting the filters or keep training to unlock more achievements!',
      unlockedOf: 'of',
      achievementsUnlocked: 'achievements unlocked',
      categories: {
        training: 'Training',
        accuracy: 'Accuracy',
        streak: 'Streak',
        level: 'Level'
      }
    },

    // Poker table
    poker: {
      pot: 'Pot',
      street: 'Street',
      position: 'Position',
      stack: 'Stack',
      yourTurn: 'YOUR TURN',
      yourChoice: 'Your Choice',
      gtoRecommended: 'GTO Recommended Action',
      excellentPlay: 'Excellent Play! 🎉',
      youChoseGto: 'You chose the GTO recommended action',
      actionNotRecommended: 'Action Not Recommended ❌',
      youChose: 'You chose',
      butGtoRecommends: 'but the GTO recommended action is',
      frequency: 'Frequency',
      equity: 'Equity',
      whyCorrectPlay: 'Why this is the correct play:',
      analysisYourDecision: 'Analysis of your decision:',
      analysisYourChoice: '⚠️ Analysis of your choice:',
      yourAction: 'Your Action',
      recommendedAction: 'Recommended Action',
      gtoFrequency: 'GTO Frequency',
      excellentDecision: '✅ Excellent decision!',
      xpGained: 'XP Gained',
      newSituation: 'New Situation',
      tryAgain: 'Try Again',
      tableActions: 'Table Actions',
      you: 'You',
      yourTurnToAct: 'Your turn to act',
      actionRequired: 'ACTION REQUIRED',
      situationContext: 'Situation Context',
      situation: 'Situation',
      cashGame: 'Cash Game',
      tournament: 'Tournament',
      players: 'Players',
      active: 'active',
      ofTable: 'of 6 at table',
      potOdds: 'Pot Odds',
      invested: 'invested',
      aggression: 'Aggression',
      present: 'Present',
      absent: 'Absent',
      passiveTable: 'Passive table',
      gtoTip: '💡 GTO Tip:',
      tips: {
        latePosition: 'In late position, you can open with a wider range and apply pressure.',
        earlyPosition: 'In early position, be more selective with your opening hands.',
        blinds: 'In the blinds, consider defending with hands that have good postflop playability.',
        withAggression: 'With aggression at the table, consider your pot odds and hand strength to continue.',
        fewPlayers: 'With few players, you can be more aggressive and bluff more frequently.',
        analyzeBoard: 'Analyze the board texture and relative strength of your hand in this situation.'
      }
    },

    // Demo
    demo: {
      title: 'Demo - GTO Trainer',
      subtitle: 'Try our training interface',
      back: 'Back',
      createAccount: 'Create Account',
      howItWorks: 'How It Works',
      howItWorksDescription: 'This is a demonstration of our training interface. You are seeing a real poker situation where you need to make a decision. Click on one of the actions below to see the GTO recommendation.',
      instructions: [
        'Analyze your cards and the situation at the table',
        'Consider your position and pot size',
        'Choose the action you consider most appropriate',
        'Receive detailed feedback on your decision'
      ],
      demoScenario: 'Demo Scenario',
      scenarioOf: 'Scenario',
      of: 'of',
      previous: '← Previous',
      next: 'Next →',
      loadingDemo: 'Loading demo situation...',
      errorLoadingDemo: 'Error loading demo situation',
      unlockMore: 'Unlock More!',
      wantMoreSituations: 'Want to See More Situations?',
      createFreeAccount: 'Create your free account and access:',
      features: [
        'Hundreds of real GTO situations',
        'Progress and level system',
        'Detailed analysis of your game',
        'Achievements and global ranking'
      ],
      createFreeAccountBtn: '🚀 Create Free Account',
      alreadyHaveAccount: 'Already have an account',
      continueDemo: 'Continue in demo',
      readyToStart: 'Ready to Start Your Training?',
      accessThousands: 'Access thousands of scenarios, track your progress and improve your poker skills.',
      resources: {
        scenarios: '10,000+ Scenarios',
        scenariosDesc: 'Train with thousands of real poker situations, from preflop to river.',
        analysis: 'Detailed Analysis',
        analysisDesc: 'Get complete explanations for each decision and learn the theory behind the plays.',
        gamified: 'Gamified Progress',
        gamifiedDesc: 'Level system, achievements and ranking to keep you motivated to improve.'
      }
    },

    // Leaderboard
    leaderboard: {
      title: 'Leaderboard',
      rank: 'Rank',
      player: 'Player',
      level: 'Level',
      points: 'Points',
      accuracy: 'Accuracy'
    },

    // Error messages
    errors: {
      generic: 'An error occurred. Please try again.',
      network: 'Connection error. Check your internet.',
      invalidCredentials: 'Invalid credentials.',
      userExists: 'User already exists.',
      invalidEmail: 'Invalid email.',
      passwordTooShort: 'Password must be at least 8 characters.',
      passwordsDontMatch: 'Passwords do not match.',
      usernameRequired: 'Username is required.',
      emailRequired: 'Email is required.',
      passwordRequired: 'Password is required.',
      usernameInvalid: 'Username must be at least 3 characters and contain only letters, numbers and _',
      confirmPasswordRequired: 'Password confirmation is required',
      registering: 'Registering...'
    },

    // Success messages
    success: {
      loginSuccess: 'Login successful!',
      registerSuccess: 'Account created successfully!',
      profileUpdated: 'Profile updated successfully!',
      achievementUnlocked: 'Achievement unlocked!'
    },

    // Buttons and general actions
    buttons: {
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      close: 'Close',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      submit: 'Submit',
      reset: 'Reset'
    }
  }
}
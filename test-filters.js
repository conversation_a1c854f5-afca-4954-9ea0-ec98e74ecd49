const { PrismaClient } = require('@prisma/client');

async function testFilters() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== TESTE 1: Busca exata por categoria ===');
    const exact = await prisma.gTOScenario.findMany({
      where: { category: '3bet Preflop' },
      select: { id: true, name: true, category: true },
      take: 3
    });
    console.log(`Encontrados ${exact.length} cenários com categoria exata "3bet Preflop"`);
    exact.forEach(s => console.log(`- ${s.name}`));
    
    console.log('\n=== TESTE 2: Busca parcial por categoria ===');
    const partial = await prisma.gTOScenario.findMany({
      where: { category: { contains: '3bet' } },
      select: { id: true, name: true, category: true },
      take: 3
    });
    console.log(`Encontrados ${partial.length} cenários com categoria contendo "3bet"`);
    partial.forEach(s => console.log(`- ${s.name} (${s.category})`));
    
    console.log('\n=== TESTE 3: Busca OR múltiplos campos ===');
    const multiple = await prisma.gTOScenario.findMany({
      where: {
        OR: [
          { category: { contains: '3bet' } },
          { name: { contains: '3bet' } },
          { tags: { contains: '3bet' } }
        ]
      },
      select: { id: true, name: true, category: true },
      take: 5
    });
    console.log(`Encontrados ${multiple.length} cenários com "3bet" em qualquer campo`);
    multiple.forEach(s => console.log(`- ${s.name} (${s.category})`));
    
    console.log('\n=== TESTE 4: Busca com filtros personalizados ===');
    const withCustomFilter = await prisma.gTOScenario.findMany({
      where: {
        customFilter: {
          name: { contains: '3bet' }
        }
      },
      include: { customFilter: true },
      take: 3
    });
    console.log(`Encontrados ${withCustomFilter.length} cenários com filtro personalizado contendo "3bet"`);
    withCustomFilter.forEach(s => console.log(`- ${s.name} (filtro: ${s.customFilter?.name})`));
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testFilters();
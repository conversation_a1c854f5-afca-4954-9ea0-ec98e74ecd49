const fs = require('fs');
const path = require('path');

// Configuração do servidor
const BASE_URL = 'http://localhost:3000';

// Função para fazer requisições
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { error: error.message };
  }
}

// Teste 1: Criar cenário manualmente via API
async function testCreateScenarioManually() {
  console.log('\n🧪 Teste 1: Criar cenário manualmente via API');
  console.log('=' .repeat(50));

  const scenarioData = {
    name: "Teste Manual: River Call com Pair",
    description: "Você tem 88 no river em board A8432. Oponente aposta pot. Pagar?",
    gameType: "cash",
    street: "river",
    position: "CO",
    stackDepth: "medium",
    playerCards: {
      card1: "8h",
      card2: "8c"
    },
    communityCards: {
      flop: ["As", "8d", "4c"],
      turn: "3h",
      river: "2s"
    },
    potSize: 100,
    playerStack: 120,
    opponentActions: [
      {
        position: "BB",
        action: "bet",
        amount: 100,
        isActive: true,
        stackSize: 120
      }
    ],
    availableActions: [
      {
        action: "fold",
        frequency: 0.1,
        isCorrect: false,
        explanation: "Fold seria muito tight com trips"
      },
      {
        action: "call",
        amount: 100,
        frequency: 0.9,
        isCorrect: true,
        explanation: "Call é obrigatório com trips no river"
      }
    ],
    correctAction: "call",
    equity: 0.95,
    frequency: 0.9,
    explanation: "Trips é uma mão muito forte que deve sempre pagar no river",
    difficulty: "beginner",
    category: "river_play",
    tags: ["value_call", "trips"]
  };

  const result = await makeRequest(`${BASE_URL}/api/scenarios/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(scenarioData)
  });

  if (result.error) {
    console.log('❌ Erro na requisição:', result.error);
    return false;
  }

  if (result.status === 200) {
    console.log('✅ Cenário criado com sucesso!');
    console.log('📋 ID do cenário:', result.data.scenario?.id);
    console.log('📝 Nome:', result.data.scenario?.name);
    return true;
  } else {
    console.log('❌ Erro na criação:', result.data.error);
    return false;
  }
}

// Teste 2: Upload de cenários via JSON
async function testUploadScenariosJSON() {
  console.log('\n🧪 Teste 2: Upload de cenários via JSON');
  console.log('=' .repeat(50));

  try {
    // Ler o arquivo de teste
    const jsonPath = path.join(__dirname, 'test-scenarios.json');
    
    if (!fs.existsSync(jsonPath)) {
      console.log('❌ Arquivo test-scenarios.json não encontrado');
      return false;
    }

    const fileContent = fs.readFileSync(jsonPath);
    const formData = new FormData();
    
    // Criar um Blob do conteúdo do arquivo
    const blob = new Blob([fileContent], { type: 'application/json' });
    formData.append('file', blob, 'test-scenarios.json');

    const result = await makeRequest(`${BASE_URL}/api/scenarios/upload`, {
      method: 'POST',
      body: formData
    });

    if (result.error) {
      console.log('❌ Erro na requisição:', result.error);
      return false;
    }

    if (result.status === 200) {
      console.log('✅ Upload realizado com sucesso!');
      console.log('📊 Cenários importados:', result.data.results?.success || 0);
      
      if (result.data.results?.errors?.length > 0) {
        console.log('⚠️ Erros encontrados:');
        result.data.results.errors.forEach(error => {
          console.log('  • ' + error);
        });
      }
      return true;
    } else {
      console.log('❌ Erro no upload:', result.data.error);
      return false;
    }

  } catch (error) {
    console.log('❌ Erro ao processar arquivo:', error.message);
    return false;
  }
}

// Teste 3: Listar cenários
async function testListScenarios() {
  console.log('\n🧪 Teste 3: Listar cenários');
  console.log('=' .repeat(50));

  const result = await makeRequest(`${BASE_URL}/api/scenarios`);

  if (result.error) {
    console.log('❌ Erro na requisição:', result.error);
    return false;
  }

  if (result.status === 200) {
    console.log('✅ Cenários listados com sucesso!');
    console.log('📊 Total de cenários:', result.data.scenarios?.length || 0);
    
    if (result.data.scenarios?.length > 0) {
      console.log('\n📋 Últimos cenários:');
      result.data.scenarios.slice(0, 3).forEach((scenario, index) => {
        console.log(`  ${index + 1}. ${scenario.name} (${scenario.difficulty})`);
      });
    }
    return true;
  } else {
    console.log('❌ Erro ao listar:', result.data.error);
    return false;
  }
}

// Teste 4: Validar estrutura do JSON de teste
function testValidateJSONStructure() {
  console.log('\n🧪 Teste 4: Validar estrutura do JSON de teste');
  console.log('=' .repeat(50));

  try {
    const jsonPath = path.join(__dirname, 'test-scenarios.json');
    const fileContent = fs.readFileSync(jsonPath, 'utf8');
    const scenarios = JSON.parse(fileContent);

    if (!Array.isArray(scenarios)) {
      console.log('❌ JSON deve ser um array');
      return false;
    }

    console.log(`✅ JSON válido com ${scenarios.length} cenários`);

    // Validar cada cenário
    const requiredFields = ['name', 'description', 'gameType', 'street', 'position', 'playerCards', 'correctAction', 'explanation', 'availableActions'];
    
    scenarios.forEach((scenario, index) => {
      console.log(`\n📋 Validando cenário ${index + 1}: ${scenario.name}`);
      
      const missingFields = requiredFields.filter(field => !scenario[field]);
      if (missingFields.length > 0) {
        console.log(`  ❌ Campos ausentes: ${missingFields.join(', ')}`);
      } else {
        console.log('  ✅ Todos os campos obrigatórios presentes');
      }

      // Validar cartas do jogador
      if (!scenario.playerCards?.card1 || !scenario.playerCards?.card2) {
        console.log('  ❌ playerCards deve ter card1 e card2');
      } else {
        console.log(`  ✅ Cartas do jogador: ${scenario.playerCards.card1}, ${scenario.playerCards.card2}`);
      }

      // Validar ações disponíveis
      if (!Array.isArray(scenario.availableActions) || scenario.availableActions.length === 0) {
        console.log('  ❌ availableActions deve ser um array não vazio');
      } else {
        console.log(`  ✅ ${scenario.availableActions.length} ações disponíveis`);
      }
    });

    return true;
  } catch (error) {
    console.log('❌ Erro ao validar JSON:', error.message);
    return false;
  }
}

// Executar todos os testes
async function runAllTests() {
  console.log('🚀 Iniciando testes das APIs de cenários');
  console.log('=' .repeat(60));

  const results = {
    jsonValidation: false,
    manualCreate: false,
    jsonUpload: false,
    listScenarios: false
  };

  // Teste 4: Validar JSON (primeiro)
  results.jsonValidation = testValidateJSONStructure();

  // Aguardar um pouco entre os testes
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 1: Criar manualmente
  results.manualCreate = await testCreateScenarioManually();

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 2: Upload JSON
  results.jsonUpload = await testUploadScenariosJSON();

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 3: Listar cenários
  results.listScenarios = await testListScenarios();

  // Resumo dos resultados
  console.log('\n📊 RESUMO DOS TESTES');
  console.log('=' .repeat(60));
  console.log(`Validação JSON:     ${results.jsonValidation ? '✅ PASSOU' : '❌ FALHOU'}`);
  console.log(`Criação Manual:     ${results.manualCreate ? '✅ PASSOU' : '❌ FALHOU'}`);
  console.log(`Upload JSON:        ${results.jsonUpload ? '✅ PASSOU' : '❌ FALHOU'}`);
  console.log(`Listagem:           ${results.listScenarios ? '✅ PASSOU' : '❌ FALHOU'}`);

  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n🎯 RESULTADO FINAL: ${totalPassed}/${totalTests} testes passaram`);

  if (totalPassed === totalTests) {
    console.log('🎉 Todos os testes passaram! Sistema funcionando corretamente.');
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique os erros acima.');
  }
}

// Verificar se o Node.js tem fetch (Node 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ Este script requer Node.js 18+ ou instale node-fetch');
  console.log('💡 Execute: npm install node-fetch');
  process.exit(1);
}

// Executar os testes
runAllTests().catch(console.error);
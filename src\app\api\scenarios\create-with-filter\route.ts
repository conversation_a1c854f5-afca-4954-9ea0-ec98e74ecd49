import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validar dados obrigatórios
    const requiredFields = ['name', 'description', 'gameType', 'street', 'position', 'playerCards', 'correctAction', 'explanation']
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Campo obrigatório: ${field}` },
          { status: 400 }
        )
      }
    }

    // Processar filtro personalizado
    let customFilterId = null
    if (data.customFilter) {
      // Verificar se o filtro existe
      let customFilter = await prisma.customFilter.findUnique({
        where: { name: data.customFilter }
      })

      // Se não existe, criar automaticamente
      if (!customFilter) {
        customFilter = await prisma.customFilter.create({
          data: {
            name: data.customFilter,
            description: data.customFilterDescription || `Filtro personalizado: ${data.customFilter}`,
            color: data.customFilterColor || '#3B82F6'
          }
        })
      }

      customFilterId = customFilter.id
    }

    // Criar o cenário
    const scenario = await prisma.gTOScenario.create({
      data: {
        name: data.name,
        nameEn: data.nameEn || data.name,
        namePt: data.namePt || data.name,
        description: data.description,
        descriptionEn: data.descriptionEn || data.description,
        descriptionPt: data.descriptionPt || data.description,
        gameType: data.gameType,
        street: data.street,
        position: data.position,
        stackDepth: data.stackDepth || 'deep',
        playerCards: JSON.stringify(data.playerCards),
        communityCards: JSON.stringify(data.communityCards || { flop: null, turn: null, river: null }),
        potSize: parseFloat(data.potSize) || 0,
        playerStack: parseFloat(data.playerStack) || 200,
        opponentActions: JSON.stringify(data.opponentActions || []),
        availableActions: JSON.stringify(data.availableActions || []),
        correctAction: data.correctAction,
        equity: parseFloat(data.equity) || 0.5,
        frequency: parseFloat(data.frequency) || 1.0,
        explanation: data.explanation,
        explanationEn: data.explanationEn || data.explanation,
        explanationPt: data.explanationPt || data.explanation,
        context: JSON.stringify(data.context || {}),
        difficulty: data.difficulty || 'intermediate',
        category: data.category || 'general',
        tags: JSON.stringify(data.tags || []),
        customFilterId: customFilterId
      },
      include: {
        customFilter: true
      }
    })

    return NextResponse.json({ 
      success: true, 
      scenario: scenario,
      message: 'Cenário criado com sucesso!' + (customFilterId ? ` Filtro "${data.customFilter}" aplicado.` : '')
    })

  } catch (error) {
    console.error('Erro ao criar cenário:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
const { PrismaClient } = require('@prisma/client');

async function checkScenarios() {
  const prisma = new PrismaClient();
  
  try {
    const count = await prisma.gTOScenario.count();
    console.log('Total de cenários no banco:', count);
    
    if (count > 0) {
      const firstScenario = await prisma.gTOScenario.findFirst();
      console.log('Primeiro cenário:', {
        id: firstScenario.id,
        name: firstScenario.name,
        street: firstScenario.street,
        position: firstScenario.position
      });
    }
    
  } catch (error) {
    console.error('Erro ao verificar cenários:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkScenarios();
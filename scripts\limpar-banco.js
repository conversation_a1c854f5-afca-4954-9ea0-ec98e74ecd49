const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function limparBanco() {
  try {
    console.log('🗑️ Limpando banco de dados completamente...')
    
    // Deletar todos os cenários
    const deleted = await prisma.gTOScenario.deleteMany({})
    console.log(`✅ ${deleted.count} cenários removidos`)
    
    // Verificar se está vazio
    const count = await prisma.gTOScenario.count()
    console.log(`📊 Cenários restantes: ${count}`)
    
    if (count === 0) {
      console.log('✅ Banco limpo com sucesso!')
    } else {
      console.log('❌ Ainda há cenários no banco')
    }
    
  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

limparBanco()
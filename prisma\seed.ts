import { PrismaClient } from '@prisma/client'
import { DEFAULT_ACHIEVEMENTS } from '../src/lib/constants'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...')

  // Limpar dados existentes
  await prisma.userAchievement.deleteMany()
  await prisma.achievement.deleteMany()
  await prisma.gTOScenario.deleteMany()

  // Criar conquistas padrão
  console.log('📊 Criando conquistas...')
  for (const achievement of DEFAULT_ACHIEVEMENTS) {
    await prisma.achievement.create({
      data: {
        name: achievement.id,
        nameEn: achievement.nameEn,
        namePt: achievement.namePt,
        description: achievement.descriptionEn,
        descriptionEn: achievement.descriptionEn,
        descriptionPt: achievement.descriptionPt,
        icon: achievement.icon,
        xpReward: achievement.xpReward,
        category: achievement.category,
        requirement: JSON.stringify(achievement.requirement)
      }
    })
  }

  // Criar cenários GTO detalhados
  console.log('🎯 Criando cenários GTO...')
  
  const scenarios = [
    {
      id: "scenario_001_btn_cbet_dry_board",
      name: "BTN C-bet on Dry Board",
      nameEn: "BTN C-bet on Dry Board", 
      namePt: "C-bet do BTN em Board Seco",
      description: "Button continuation bet on A-7-2 rainbow after opening preflop",
      descriptionEn: "Button continuation bet on A-7-2 rainbow after opening preflop",
      descriptionPt: "Continuation bet do Button em A-7-2 rainbow após abertura pré-flop",
      gameType: "cash",
      street: "flop",
      position: "BTN",
      stackDepth: "deep",
      playerCards: JSON.stringify({ card1: "Kh", card2: "Qd" }),
      communityCards: JSON.stringify({ flop: ["As", "7c", "2h"] }),
      potSize: 15.0,
      playerStack: 194.0,
      opponentActions: JSON.stringify([
        { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
        { position: "MP", action: "fold", isActive: false, stackSize: 200 },
        { position: "CO", action: "fold", isActive: false, stackSize: 200 },
        { position: "SB", action: "fold", isActive: false, stackSize: 199 },
        { position: "BB", action: "call", amount: 4, isActive: true, stackSize: 194 }
      ]),
      availableActions: JSON.stringify([
        {
          action: "fold",
          isCorrect: false,
          frequency: 0.05,
          explanation: "Foldar aqui seria extremamente fraco. Você tem overcards e posição."
        },
        {
          action: "check",
          isCorrect: false,
          frequency: 0.20,
          explanation: "Check é passivo demais. Você deve apostar por valor e proteção."
        },
        {
          action: "bet",
          amount: 10,
          sizing: "medium",
          isCorrect: true,
          frequency: 0.75,
          explanation: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range."
        }
      ]),
      correctAction: "bet",
      equity: 0.35,
      frequency: 0.75,
      explanation: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range.",
      explanationEn: "Perfect c-bet spot! You have overcards, position, and this dry board favors your range.",
      explanationPt: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range.",
      context: JSON.stringify({
        effectiveStacks: 194,
        potOdds: 0.67,
        hasAggression: true,
        lastAggressor: "BTN",
        boardTexture: "dry",
        handStrength: "medium"
      }),
      difficulty: "intermediate",
      category: "cbet_spots",
      tags: JSON.stringify(["position_play", "continuation_bet", "dry_board", "overcards"])
    },

    {
      id: "scenario_002_bb_vs_3bet_decision",
      name: "BB vs CO 3-bet with AJo",
      nameEn: "BB vs CO 3-bet with AJo",
      namePt: "BB vs 3-bet do CO com AJo",
      description: "Big blind facing 3-bet from cutoff with AJ offsuit",
      descriptionEn: "Big blind facing 3-bet from cutoff with AJ offsuit",
      descriptionPt: "Big blind enfrentando 3-bet do cutoff com AJ offsuit",
      gameType: "cash",
      street: "preflop",
      position: "BB",
      stackDepth: "deep",
      playerCards: JSON.stringify({ card1: "Ah", card2: "Jc" }),
      communityCards: JSON.stringify({}),
      potSize: 37.0,
      playerStack: 182.0,
      opponentActions: JSON.stringify([
        { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
        { position: "MP", action: "fold", isActive: false, stackSize: 200 },
        { position: "CO", action: "raise", amount: 18, isActive: true, stackSize: 182 },
        { position: "BTN", action: "fold", isActive: false, stackSize: 200 },
        { position: "SB", action: "fold", isActive: false, stackSize: 199 }
      ]),
      availableActions: JSON.stringify([
        {
          action: "fold",
          isCorrect: true,
          frequency: 0.70,
          explanation: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão."
        },
        {
          action: "call",
          amount: 16,
          isCorrect: false,
          frequency: 0.25,
          explanation: "Call é marginal. Você recebe boas odds mas ficará fora de posição."
        },
        {
          action: "raise",
          amount: 54,
          sizing: "large",
          isCorrect: false,
          frequency: 0.05,
          explanation: "4-bet com AJo é muito solto. Reserve para mãos premium."
        }
      ]),
      correctAction: "fold",
      equity: 0.42,
      frequency: 0.70,
      explanation: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão.",
      explanationEn: "AJo is at the bottom of your calling range vs CO 3-bet. Folding is standard.",
      explanationPt: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão.",
      context: JSON.stringify({
        effectiveStacks: 182,
        potOdds: 0.43,
        hasAggression: true,
        lastAggressor: "CO",
        boardTexture: "preflop",
        handStrength: "medium"
      }),
      difficulty: "advanced",
      category: "3bet_defense",
      tags: JSON.stringify(["preflop", "3bet_defense", "position_disadvantage", "marginal_hand"])
    },

    {
      id: "scenario_003_river_bluff_catch",
      name: "River Bluff Catch Decision",
      nameEn: "River Bluff Catch Decision",
      namePt: "Decisão de Bluff Catch no River",
      description: "Facing large river bet with trips - bluff catch spot",
      descriptionEn: "Facing large river bet with trips - bluff catch spot",
      descriptionPt: "Enfrentando aposta grande no river com trinca - spot de bluff catch",
      gameType: "cash",
      street: "river",
      position: "BB",
      stackDepth: "medium",
      playerCards: JSON.stringify({ card1: "Jh", card2: "Js" }),
      communityCards: JSON.stringify({ 
        flop: ["Kc", "Jd", "7s"], 
        turn: "4h", 
        river: "Ac" 
      }),
      potSize: 240.0,
      playerStack: 80.0,
      opponentActions: JSON.stringify([
        { position: "BTN", action: "bet", amount: 120, isActive: true, stackSize: 80 }
      ]),
      availableActions: JSON.stringify([
        {
          action: "fold",
          isCorrect: false,
          frequency: 0.35,
          explanation: "Foldar trinca é muito apertado. Oponente pode ter muitos bluffs."
        },
        {
          action: "call",
          amount: 120,
          isCorrect: true,
          frequency: 0.65,
          explanation: "Excelente bluff catch! Você tem trinca e o range do oponente inclui muitos bluffs."
        }
      ]),
      correctAction: "call",
      equity: 0.55,
      frequency: 0.65,
      explanation: "Excelente bluff catch! Você tem trinca e o range do oponente inclui muitos bluffs.",
      explanationEn: "Great bluff catch! You have trips and opponent's range includes many bluffs.",
      explanationPt: "Excelente bluff catch! Você tem trinca e o range do oponente inclui muitos bluffs.",
      context: JSON.stringify({
        effectiveStacks: 80,
        potOdds: 0.50,
        hasAggression: true,
        lastAggressor: "BTN",
        boardTexture: "coordinated",
        handStrength: "strong"
      }),
      difficulty: "advanced",
      category: "river_decisions",
      tags: JSON.stringify(["bluff_catch", "river_play", "trips", "large_bet"])
    }
  ]

  for (const scenario of scenarios) {
    await prisma.gTOScenario.create({
      data: scenario
    })
  }

  console.log('✅ Seed concluído com sucesso!')
  console.log(`📊 ${DEFAULT_ACHIEVEMENTS.length} conquistas criadas`)
  console.log(`🎯 ${scenarios.length} cenários GTO criados`)
}

main()
  .catch((e) => {
    console.error('❌ Erro durante o seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
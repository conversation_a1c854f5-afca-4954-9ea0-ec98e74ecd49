'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  <PERSON>L<PERSON><PERSON>, 
  Shu<PERSON>, 
  BookOpen, 
  Eye,
  EyeOff,
  Filter,
  RotateCcw
} from 'lucide-react'
import Link from 'next/link'
import { useLanguage } from '@/hooks/useLanguage'
import { User, TrainingScenario } from '@/types'
// import Navbar from '@/components/dashboard/Navbar' // Removido - componente não existe
import PokerTable from '@/components/poker/PokerTable'
import { getRandomScenarios } from '@/lib/scenario-service'

export default function PracticePage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [currentScenario, setCurrentScenario] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showAnswer, setShowAnswer] = useState(false)
  const [userAction, setUserAction] = useState<any>(null)
  const [actionFeedback, setActionFeedback] = useState<{
    show: boolean
    isCorrect: boolean
    message: string
  }>({ show: false, isCorrect: false, message: '' })
  const [filters, setFilters] = useState({
    category: 'all',
    difficulty: 'all',
    position: 'all'
  })
  const [lastScenarioIds, setLastScenarioIds] = useState<string[]>([])
  const [practiceStats, setPracticeStats] = useState({
    scenariosViewed: 0,
    categoriesExplored: new Set<string>()
  })

  useEffect(() => {
    // Criar usuário mock para teste
    const mockUser = {
      id: 'mock-user',
      username: 'test-user',
      language: 'pt'
    }
    
    setUser(mockUser)
    
    // Verificar se há parâmetros de categoria na URL
    const urlParams = new URLSearchParams(window.location.search)
    const categoryParam = urlParams.get('category')
    
    if (categoryParam) {
      const newFilters = {
        ...filters,
        category: categoryParam
      }
      setFilters(newFilters)
      // Passar os filtros diretamente para garantir que sejam aplicados
      loadRandomScenario(newFilters)
    } else {
      loadRandomScenario()
    }
  }, [router])



  const loadRandomScenario = async (customFilters?: any) => {
    try {
      setIsLoading(true)
      setShowAnswer(false)
      
      // Usar filtros customizados se fornecidos, senão usar os do estado
      const currentFilters = customFilters || filters
      console.log('Carregando cenário com filtros:', currentFilters)
      
      let attempts = 0
      let newScenario = null
      
      // Tentar até 5 vezes para evitar repetições
      while (attempts < 5) {
        // Construir URL com filtros - buscar mais cenários para ter opções
        const params = new URLSearchParams()
        if (currentFilters.category !== 'all') params.append('category', currentFilters.category)
        if (currentFilters.difficulty !== 'all') params.append('difficulty', currentFilters.difficulty)
        if (currentFilters.position !== 'all') params.append('position', currentFilters.position)
        params.append('count', '5') // Buscar 5 cenários para ter opções
        
        const url = params.toString() 
          ? `/api/scenarios/filtered?${params.toString()}`
          : '/api/scenarios/demo'
        
        const response = await fetch(url)
        const data = await response.json()
        
        if (data.success && data.scenarios && data.scenarios.length > 0) {
          // Filtrar cenários que não estão na lista dos últimos mostrados
          const availableScenarios = data.scenarios.filter(
            (scenario: any) => !lastScenarioIds.includes(scenario.id)
          )
          
          if (availableScenarios.length > 0) {
            // Pegar um cenário aleatório dos disponíveis
            newScenario = availableScenarios[Math.floor(Math.random() * availableScenarios.length)]
            break
          } else if (data.scenarios.length > 0) {
            // Se todos os cenários já foram mostrados, pegar qualquer um
            newScenario = data.scenarios[0]
            break
          }
        }
        
        attempts++
      }
      
      if (newScenario) {
        console.log('Cenário carregado:', newScenario.name)
        setCurrentScenario(newScenario)
        
        // Atualizar lista dos últimos cenários (manter apenas os últimos 10)
        setLastScenarioIds(prev => {
          const updated = [newScenario.id, ...prev].slice(0, 10)
          return updated
        })
        
        // Atualizar estatísticas de prática
        setPracticeStats(prev => ({
          scenariosViewed: prev.scenariosViewed + 1,
          categoriesExplored: new Set(Array.from(prev.categoriesExplored).concat([newScenario.gameState?.street || 'general']))
        }))
      } else {
        console.error('Nenhum cenário retornado pela API')
        setCurrentScenario(null)
      }
      
    } catch (error) {
      console.error('Erro ao carregar cenário:', error)
      setCurrentScenario(null)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }))
  }

  const applyFilters = () => {
    loadRandomScenario()
  }

  const resetFilters = () => {
    setFilters({
      category: 'all',
      difficulty: 'all',
      position: 'all'
    })
    setTimeout(() => {
      loadRandomScenario()
    }, 100)
  }

  const handleUserAction = (action: any) => {
    if (!currentScenario) return

    // Extrair a ação correta do cenário
    const correctAction = currentScenario.recommendation?.action?.type
    const userActionType = typeof action === 'string' ? action : action.type
    
    const isCorrect = userActionType === correctAction
    
    console.log('Ação do usuário:', userActionType)
    console.log('Ação correta:', correctAction)
    console.log('Está correto:', isCorrect)
    
    // Salvar a ação do usuário
    setUserAction(action)
    
    // Mostrar feedback
    const feedbackMessage = isCorrect 
      ? `✅ Correto! Você escolheu ${userActionType}.`
      : `❌ Incorreto. Você escolheu ${userActionType}, mas a ação correta seria ${correctAction}.`
    
    setActionFeedback({
      show: true,
      isCorrect,
      message: feedbackMessage
    })

    // Esconder feedback após 4 segundos
    setTimeout(() => {
      setActionFeedback({ show: false, isCorrect: false, message: '' })
    }, 4000)
  }

  const resetScenario = () => {
    setUserAction(null)
    setShowAnswer(false)
    setActionFeedback({ show: false, isCorrect: false, message: '' })
    loadRandomScenario()
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/dashboard/training" 
                className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar ao Treinamento
              </Link>
              <div className="w-px h-6 bg-gray-600"></div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  Prática Livre
                </h1>
                <p className="text-gray-400 text-sm">
                  Explore cenários sem pressão de tempo
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <BookOpen className="w-4 h-4" />
              <span>{practiceStats.scenariosViewed} cenários visualizados</span>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Toast */}
      {actionFeedback.show && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`fixed top-20 left-1/2 transform -translate-x-1/2 z-50 p-4 rounded-lg shadow-lg max-w-md ${
            actionFeedback.isCorrect 
              ? 'bg-green-600 border border-green-500' 
              : 'bg-red-600 border border-red-500'
          }`}
        >
          <p className="text-white font-medium">{actionFeedback.message}</p>
        </motion.div>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Área Principal - Mesa de Poker */}
          <div className="lg:col-span-3">
            {isLoading ? (
              <div className="bg-gray-800 rounded-xl border border-gray-700 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Carregando cenário...</p>
                </div>
              </div>
            ) : currentScenario ? (
              <div className="space-y-6">
                <div className="mb-4 p-4 bg-gray-700 rounded-lg">
                  <h3 className="text-white font-bold">{currentScenario.name}</h3>
                  <p className="text-gray-300 text-sm">{currentScenario.description}</p>
                  <p className="text-gray-400 text-xs mt-2">
                    Street: {currentScenario.gameState?.street} | 
                    Position: {currentScenario.gameState?.position?.name} | 
                    Pot: ${currentScenario.gameState?.pot}
                  </p>
                </div>
                <PokerTable 
                  gameState={currentScenario?.gameState}
                  onAction={handleUserAction} // Agora com callback funcional
                  showRecommendation={false} // Não mostrar recomendação automaticamente
                  availableActions={currentScenario?.availableActions}
                />
                
                {/* Resposta e Explicação */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800 rounded-xl border border-gray-700 p-6"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-white">Análise do Cenário</h3>
                    <button
                      onClick={() => setShowAnswer(!showAnswer)}
                      className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      {showAnswer ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      <span>{showAnswer ? 'Ocultar' : 'Mostrar'} Resposta</span>
                    </button>
                  </div>
                  
                  {showAnswer && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-4"
                    >
                      <div className="bg-green-900/30 border border-green-700 rounded-lg p-4">
                        <h4 className="text-green-400 font-bold mb-2">Ação Correta:</h4>
                        <p className="text-white text-lg font-medium">
                          {currentScenario.recommendation?.action?.type || 'N/A'}
                          {currentScenario.recommendation?.action?.amount && 
                            ` ($${currentScenario.recommendation.action.amount})`
                          }
                        </p>
                        <p className="text-green-300 text-sm mt-1">
                          Frequência: {Math.round((currentScenario.recommendation?.frequency || 0) * 100)}%
                        </p>
                      </div>
                      
                      <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
                        <h4 className="text-blue-400 font-bold mb-2">Explicação:</h4>
                        <p className="text-gray-300 leading-relaxed">
                          {currentScenario.recommendation?.explanation || 'Explicação não disponível'}
                        </p>
                      </div>
                      
                      <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-4">
                        <h4 className="text-purple-400 font-bold mb-2">Informações Adicionais:</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-400">Equity:</span>
                            <span className="text-white ml-2">
                              {Math.round((currentScenario.recommendation?.equity || 0) * 100)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-400">Pot Size:</span>
                            <span className="text-white ml-2">${currentScenario.gameState?.pot || 0}</span>
                          </div>
                        </div>
                      </div>
                      
                      {currentScenario.availableActions && currentScenario.availableActions.length > 0 && (
                        <div>
                          <h4 className="text-gray-400 font-medium mb-2">Ações Disponíveis:</h4>
                          <div className="space-y-2">
                            {currentScenario.availableActions.map((action: any, index: number) => (
                              <div
                                key={index}
                                className={`p-3 rounded-lg border ${
                                  action.isCorrect 
                                    ? 'bg-green-900/20 border-green-700' 
                                    : 'bg-gray-800 border-gray-600'
                                }`}
                              >
                                <div className="flex justify-between items-center mb-1">
                                  <span className={`font-medium ${
                                    action.isCorrect ? 'text-green-400' : 'text-gray-300'
                                  }`}>
                                    {action.action}
                                    {action.amount && ` ($${action.amount})`}
                                  </span>
                                  <span className="text-sm text-gray-400">
                                    {Math.round(action.frequency * 100)}%
                                  </span>
                                </div>
                                <p className="text-sm text-gray-400">{action.explanation}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </motion.div>
              </div>
            ) : (
              <div className="bg-gray-800 rounded-xl border border-gray-700 p-8 text-center">
                <p className="text-gray-400">Nenhum cenário disponível</p>
              </div>
            )}
          </div>

          {/* Sidebar - Filtros e Controles */}
          <div className="space-y-6">
            {/* Filtros */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white">Filtros</h3>
                <button
                  onClick={resetFilters}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Categoria
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) => {
                      handleFilterChange('category', e.target.value)
                      // Recarregar cenário com novo filtro automaticamente
                      setTimeout(() => loadRandomScenario(), 100)
                    }}
                    className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Todas</option>
                    <option value="preflop">Preflop (67)</option>
                    <option value="3bet">3-bet (29)</option>
                    <option value="cbet">C-bet (29)</option>
                    <option value="turn_aggression">Turn Aggression (41)</option>
                    <option value="river_value">River Value (24)</option>
                    <option value="donk_lead">Donk Lead (22)</option>
                    <option value="river_aggression">River Aggression (20)</option>
                    <option value="turn_play">Turn Play (20)</option>
                    <option value="river_bluff">River Bluff (18)</option>
                    <option value="float">Float (18)</option>
                    <option value="check_raise">Check Raise (14)</option>
                    <option value="balance">Balance (10)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Dificuldade
                  </label>
                  <select
                    value={filters.difficulty}
                    onChange={(e) => {
                      handleFilterChange('difficulty', e.target.value)
                      // Recarregar cenário com novo filtro automaticamente
                      setTimeout(() => loadRandomScenario(), 100)
                    }}
                    className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Todas</option>
                    <option value="beginner">Iniciante</option>
                    <option value="intermediate">Intermediário</option>
                    <option value="advanced">Avançado</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Posição
                  </label>
                  <select
                    value={filters.position}
                    onChange={(e) => {
                      handleFilterChange('position', e.target.value)
                      // Recarregar cenário com novo filtro automaticamente
                      setTimeout(() => loadRandomScenario(), 100)
                    }}
                    className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Todas</option>
                    <option value="UTG">UTG</option>
                    <option value="MP">MP</option>
                    <option value="CO">CO</option>
                    <option value="BTN">BTN</option>
                    <option value="SB">SB</option>
                    <option value="BB">BB</option>
                  </select>
                </div>
                
                <button
                  onClick={applyFilters}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Filter className="w-4 h-4" />
                  <span>Aplicar Filtros</span>
                </button>
              </div>
            </motion.div>

            {/* Controles */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">Controles</h3>
              
              <div className="space-y-3">
                <button
                  onClick={loadRandomScenario}
                  className="w-full flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Shuffle className="w-4 h-4" />
                  <span>Novo Cenário</span>
                </button>
                
                <button
                  onClick={() => setShowAnswer(!showAnswer)}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {showAnswer ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  <span>{showAnswer ? 'Ocultar' : 'Ver'} Resposta</span>
                </button>
                
                <button
                  onClick={resetScenario}
                  className="w-full flex items-center justify-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span>Resetar Cenário</span>
                </button>
              </div>
            </motion.div>

            {/* Estatísticas da Sessão */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">Sessão Atual</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Cenários Vistos</span>
                  <span className="text-white font-bold">{practiceStats.scenariosViewed}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Categorias</span>
                  <span className="text-white font-bold">{practiceStats.categoriesExplored.size}</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
const mysql = require('mysql2/promise');

async function setupDatabase() {
  try {
    // Conectar ao MySQL sem especificar o banco
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: ''
    });

    console.log('✅ Conectado ao MySQL');

    // Criar o banco de dados se não existir
    await connection.execute('CREATE DATABASE IF NOT EXISTS gto_poker_trainer');
    console.log('✅ Banco de dados "gto_poker_trainer" criado/verificado');

    await connection.end();
    console.log('✅ Setup do MySQL concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao configurar MySQL:', error.message);
    process.exit(1);
  }
}

setupDatabase();
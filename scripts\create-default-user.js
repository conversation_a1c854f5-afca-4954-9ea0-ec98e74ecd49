const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function createDefaultUser() {
  try {
    // Verificar se já existe um usuário padrão
    const existingUser = await prisma.user.findUnique({
      where: { username: 'player' }
    })

    if (existingUser) {
      console.log('✅ Usuário padrão já existe:', existingUser.username)
      return existingUser
    }

    // Criar usuário padrão
    const defaultUser = await prisma.user.create({
      data: {
        username: 'player',
        language: 'pt',
        level: 1,
        xp: 0,
        totalPoints: 0
      }
    })

    // Criar estatísticas iniciais para o usuário
    await prisma.userStatistics.create({
      data: {
        userId: defaultUser.id,
        totalHands: 0,
        correctDecisions: 0,
        averageEquity: 0.0,
        preflopAccuracy: 0.0,
        flopAccuracy: 0.0,
        turnAccuracy: 0.0,
        riverAccuracy: 0.0,
        aggressionFrequency: 0.0,
        foldFrequency: 0.0,
        callFrequency: 0.0,
        raiseFrequency: 0.0,
        cBetFrequency: 0.0,
        checkRaiseFrequency: 0.0,
        currentStreak: 0,
        longestStreak: 0
      }
    })

    console.log('✅ Usuário padrão criado com sucesso!')
    console.log('   Username:', defaultUser.username)
    console.log('   ID:', defaultUser.id)
    console.log('   Language:', defaultUser.language)

    return defaultUser

  } catch (error) {
    console.error('❌ Erro ao criar usuário padrão:', error)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  createDefaultUser()
}

module.exports = { createDefaultUser }
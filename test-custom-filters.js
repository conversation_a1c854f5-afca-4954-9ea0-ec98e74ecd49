// Teste do sistema de filtros personalizados

const BASE_URL = 'http://localhost:3000';

// Função para fazer requisições
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { error: error.message };
  }
}

// Teste 1: Criar filtro personalizado
async function testCreateCustomFilter() {
  console.log('\n🧪 Teste 1: Criar filtro personalizado');
  console.log('='.repeat(50));

  const filterData = {
    name: "Teste Cbet Flop",
    description: "Cenários de teste para continuation bet no flop",
    color: "#10B981"
  };

  const result = await makeRequest(`${BASE_URL}/api/custom-filters`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(filterData)
  });

  if (result.error) {
    console.log('❌ Erro na requisição:', result.error);
    return false;
  }

  if (result.status === 200) {
    console.log('✅ Filtro criado com sucesso!');
    console.log('📋 ID do filtro:', result.data.filter?.id);
    console.log('📝 Nome:', result.data.filter?.name);
    console.log('🎨 Cor:', result.data.filter?.color);
    return true;
  } else {
    console.log('❌ Erro na criação:', result.data.error);
    return false;
  }
}

// Teste 2: Listar filtros personalizados
async function testListCustomFilters() {
  console.log('\n🧪 Teste 2: Listar filtros personalizados');
  console.log('='.repeat(50));

  const result = await makeRequest(`${BASE_URL}/api/custom-filters`);

  if (result.error) {
    console.log('❌ Erro na requisição:', result.error);
    return false;
  }

  if (result.status === 200) {
    console.log('✅ Filtros listados com sucesso!');
    console.log('📊 Total de filtros:', result.data.filters?.length || 0);
    
    if (result.data.filters?.length > 0) {
      console.log('\n📋 Filtros encontrados:');
      result.data.filters.forEach((filter, index) => {
        console.log(`  ${index + 1}. ${filter.name} (${filter._count?.scenarios || 0} cenários)`);
      });
    }
    return true;
  } else {
    console.log('❌ Erro ao listar:', result.data.error);
    return false;
  }
}

// Teste 3: Criar cenário com filtro personalizado
async function testCreateScenarioWithFilter() {
  console.log('\n🧪 Teste 3: Criar cenário com filtro personalizado');
  console.log('='.repeat(50));

  const scenarioData = {
    name: "Teste: C-bet com Filtro",
    description: "Cenário de teste com filtro personalizado",
    gameType: "cash",
    street: "flop",
    position: "BTN",
    playerCards: {
      card1: "As",
      card2: "Kh"
    },
    communityCards: {
      flop: ["Ac", "7d", "2s"],
      turn: null,
      river: null
    },
    potSize: 20,
    playerStack: 190,
    availableActions: [
      {
        action: "bet",
        frequency: 1.0,
        isCorrect: true,
        explanation: "Bet para value com top pair"
      }
    ],
    correctAction: "bet",
    equity: 0.85,
    frequency: 0.9,
    explanation: "TPTK deve apostar para value",
    difficulty: "beginner",
    category: "cbet",
    customFilter: "Teste Cbet Flop"
  };

  const result = await makeRequest(`${BASE_URL}/api/scenarios/create-with-filter`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(scenarioData)
  });

  if (result.error) {
    console.log('❌ Erro na requisição:', result.error);
    return false;
  }

  if (result.status === 200) {
    console.log('✅ Cenário criado com filtro!');
    console.log('📋 ID do cenário:', result.data.scenario?.id);
    console.log('📝 Nome:', result.data.scenario?.name);
    console.log('🏷️ Filtro:', result.data.scenario?.customFilter?.name);
    return true;
  } else {
    console.log('❌ Erro na criação:', result.data.error);
    return false;
  }
}

// Teste 4: Upload JSON com filtro personalizado
async function testUploadWithCustomFilter() {
  console.log('\n🧪 Teste 4: Upload JSON com filtro personalizado');
  console.log('='.repeat(50));

  const jsonData = [
    {
      "name": "Teste Upload com Filtro",
      "description": "Cenário via upload com filtro personalizado",
      "gameType": "cash",
      "street": "preflop",
      "position": "UTG",
      "playerCards": {
        "card1": "Ah",
        "card2": "Qc"
      },
      "correctAction": "raise",
      "explanation": "AQ deve abrir UTG",
      "availableActions": [
        {
          "action": "raise",
          "frequency": 1.0,
          "isCorrect": true,
          "explanation": "Abertura padrão"
        }
      ],
      "customFilter": "Teste Preflop Open",
      "customFilterDescription": "Cenários de abertura preflop",
      "customFilterColor": "#F59E0B"
    }
  ];

  try {
    const jsonString = JSON.stringify(jsonData, null, 2);
    const formData = new FormData();
    const blob = new Blob([jsonString], { type: 'application/json' });
    formData.append('file', blob, 'test-with-filter.json');

    const result = await makeRequest(`${BASE_URL}/api/scenarios/upload`, {
      method: 'POST',
      body: formData
    });

    if (result.error) {
      console.log('❌ Erro na requisição:', result.error);
      return false;
    }

    if (result.status === 200) {
      console.log('✅ Upload com filtro realizado!');
      console.log('📊 Cenários importados:', result.data.results?.success || 0);
      
      if (result.data.results?.errors?.length > 0) {
        console.log('⚠️ Erros encontrados:');
        result.data.results.errors.forEach(error => {
          console.log('  • ' + error);
        });
      }
      return true;
    } else {
      console.log('❌ Erro no upload:', result.data.error);
      return false;
    }

  } catch (error) {
    console.log('❌ Erro ao processar JSON:', error.message);
    return false;
  }
}

// Executar todos os testes
async function runCustomFilterTests() {
  console.log('🚀 Iniciando testes de filtros personalizados');
  console.log('='.repeat(60));

  const results = {
    createFilter: false,
    listFilters: false,
    createScenarioWithFilter: false,
    uploadWithFilter: false
  };

  // Teste 1: Criar filtro
  results.createFilter = await testCreateCustomFilter();
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 2: Listar filtros
  results.listFilters = await testListCustomFilters();
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 3: Criar cenário com filtro
  results.createScenarioWithFilter = await testCreateScenarioWithFilter();
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Teste 4: Upload com filtro
  results.uploadWithFilter = await testUploadWithCustomFilter();

  // Resumo dos resultados
  console.log('\n📊 RESUMO DOS TESTES');
  console.log('='.repeat(60));
  console.log(`✅ Criar filtro: ${results.createFilter ? 'PASSOU' : 'FALHOU'}`);
  console.log(`✅ Listar filtros: ${results.listFilters ? 'PASSOU' : 'FALHOU'}`);
  console.log(`✅ Cenário com filtro: ${results.createScenarioWithFilter ? 'PASSOU' : 'FALHOU'}`);
  console.log(`✅ Upload com filtro: ${results.uploadWithFilter ? 'PASSOU' : 'FALHOU'}`);

  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Resultado final: ${totalPassed}/${totalTests} testes passaram`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 Todos os testes passaram! Sistema de filtros personalizados funcionando corretamente.');
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique os logs acima para mais detalhes.');
  }
}

// Executar os testes se o arquivo for executado diretamente
if (typeof window === 'undefined') {
  runCustomFilterTests().catch(console.error);
}

// Exportar para uso em outros arquivos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runCustomFilterTests,
    testCreateCustomFilter,
    testListCustomFilters,
    testCreateScenarioWithFilter,
    testUploadWithCustomFilter
  };
}
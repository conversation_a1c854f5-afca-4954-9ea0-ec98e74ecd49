// Exemplo de estrutura de dados para situações GTO reais
// Este arquivo serve como modelo para popular o banco de dados

export interface GTOScenarioData {
    id: string
    name: string
    nameEn: string
    namePt: string
    description: string
    descriptionEn: string
    descriptionPt: string

    // Configurações da mesa
    gameType: 'cash' | 'tournament'
    street: 'preflop' | 'flop' | 'turn' | 'river'
    blinds: {
        smallBlind: number
        bigBlind: number
    }

    // Cartas da situação
    playerCards: {
        card1: string // Ex: "As" (Ás de espadas)
        card2: string // Ex: "Kh" (Rei de copas)
    }
    communityCards: {
        flop?: string[] // Ex: ["9h", "7c", "2d"]
        turn?: string   // Ex: "Qs"
        river?: string  // Ex: "4h"
    }

    // Posição do jogador
    playerPosition: 'UTG' | 'MP' | 'CO' | 'BTN' | 'SB' | 'BB'
    playerStack: number

    // Ações dos oponentes até o momento
    opponentActions: Array<{
        position: 'UTG' | 'MP' | 'CO' | 'BTN' | 'SB' | 'BB'
        action: 'fold' | 'call' | 'raise' | 'check' | 'bet'
        amount?: number
        stackBefore: number
        stackAfter: number
        isActive: boolean
    }>

    // Pot atual
    potSize: number

    // Opções de ação disponíveis para o jogador
    availableActions: Array<{
        action: 'fold' | 'call' | 'raise' | 'check' | 'bet'
        amount?: number
        sizing?: 'small' | 'medium' | 'large' | 'overbet'
        isCorrect: boolean
        frequency: number // 0-1 (frequência GTO)
        equity?: number   // 0-1 (equidade da mão)
        explanation: string
        explanationEn: string
        explanationPt: string
    }>

    // Contexto adicional
    context: {
        effectiveStacks: number
        potOdds: number
        hasAggression: boolean
        lastAggressor?: string
        boardTexture: 'dry' | 'wet' | 'coordinated' | 'rainbow'
        handStrength: 'nuts' | 'strong' | 'medium' | 'weak' | 'bluff'
    }

    // Configurações de dificuldade
    difficulty: 'beginner' | 'intermediate' | 'advanced'
    category: string // Ex: "3bet_pots", "cbet_spots", "river_decisions"
    tags: string[]   // Ex: ["position_play", "bluff_catch", "value_bet"]
}

// 5 Situações GTO reais e detalhadas para o banco de dados
export const realGTOScenarios = [
  {
    id: "scenario_001_btn_cbet_dry_board",
    name: "BTN C-bet on Dry Board",
    nameEn: "BTN C-bet on Dry Board", 
    namePt: "C-bet do BTN em Board Seco",
    description: "Button continuation bet on A-7-2 rainbow after opening preflop",
    descriptionEn: "Button continuation bet on A-7-2 rainbow after opening preflop",
    descriptionPt: "Continuation bet do Button em A-7-2 rainbow após abertura pré-flop",
    gameType: "cash",
    street: "flop",
    position: "BTN",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Kh", card2: "Qd" }),
    communityCards: JSON.stringify({ flop: ["As", "7c", "2h"] }),
    potSize: 15.0,
    playerStack: 194.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "fold", isActive: false, stackSize: 200 },
      { position: "CO", action: "fold", isActive: false, stackSize: 200 },
      { position: "SB", action: "fold", isActive: false, stackSize: 199 },
      { position: "BB", action: "call", amount: 4, isActive: true, stackSize: 194 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.05,
        explanation: "Foldar aqui seria extremamente fraco. Você tem overcards e posição.",
        explanationEn: "Folding here would be extremely weak. You have overcards and position.",
        explanationPt: "Foldar aqui seria extremamente fraco. Você tem overcards e posição."
      },
      {
        action: "check",
        isCorrect: false,
        frequency: 0.20,
        explanation: "Check é passivo demais. Você deve apostar por valor e proteção.",
        explanationEn: "Checking is too passive. You should bet for value and protection.",
        explanationPt: "Check é passivo demais. Você deve apostar por valor e proteção."
      },
      {
        action: "bet",
        amount: 10,
        sizing: "medium",
        isCorrect: true,
        frequency: 0.75,
        explanation: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range. Aposte 2/3 do pot por valor e para foldar mãos piores.",
        explanationEn: "Perfect c-bet spot! You have overcards, position, and this dry board favors your range. Bet 2/3 pot for value and to fold out worse hands.",
        explanationPt: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range. Aposte 2/3 do pot por valor e para foldar mãos piores."
      }
    ]),
    correctAction: "bet",
    equity: 0.35,
    frequency: 0.75,
    explanation: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range. Aposte 2/3 do pot por valor e para foldar mãos piores.",
    explanationEn: "Perfect c-bet spot! You have overcards, position, and this dry board favors your range. Bet 2/3 pot for value and to fold out worse hands.",
    explanationPt: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range. Aposte 2/3 do pot por valor e para foldar mãos piores.",
    context: JSON.stringify({
      effectiveStacks: 194,
      potOdds: 0.67,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "dry",
      handStrength: "medium"
    }),
    difficulty: "intermediate",
    category: "cbet_spots",
    tags: JSON.stringify(["position_play", "continuation_bet", "dry_board", "overcards"])
  },

  {
    id: "scenario_002_bb_vs_3bet_decision",
    name: "BB vs CO 3-bet with AJo",
    nameEn: "BB vs CO 3-bet with AJo",
    namePt: "BB vs 3-bet do CO com AJo",
    description: "Big blind facing 3-bet from cutoff with AJ offsuit",
    descriptionEn: "Big blind facing 3-bet from cutoff with AJ offsuit",
    descriptionPt: "Big blind enfrentando 3-bet do cutoff com AJ offsuit",
    gameType: "cash",
    street: "preflop",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Ah", card2: "Jc" }),
    communityCards: JSON.stringify({}),
    potSize: 37.0,
    playerStack: 182.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "fold", isActive: false, stackSize: 200 },
      { position: "CO", action: "raise", amount: 18, isActive: true, stackSize: 182 },
      { position: "BTN", action: "fold", isActive: false, stackSize: 200 },
      { position: "SB", action: "fold", isActive: false, stackSize: 199 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: true,
        frequency: 0.70,
        explanation: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão e evita jogar um spot difícil fora de posição.",
        explanationEn: "AJo is at the bottom of your calling range vs CO 3-bet. Folding is standard and prevents playing a difficult spot out of position.",
        explanationPt: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão e evita jogar um spot difícil fora de posição."
      },
      {
        action: "call",
        amount: 16,
        isCorrect: false,
        frequency: 0.25,
        explanation: "Call é marginal. Você recebe boas odds mas ficará fora de posição no pós-flop com uma mão que não joga bem.",
        explanationEn: "Calling is marginal. You're getting good odds but will be out of position postflop with a hand that doesn't play well.",
        explanationPt: "Call é marginal. Você recebe boas odds mas ficará fora de posição no pós-flop com uma mão que não joga bem."
      },
      {
        action: "raise",
        amount: 54,
        sizing: "large",
        isCorrect: false,
        frequency: 0.05,
        explanation: "4-bet com AJo é muito solto. Reserve seus 4-bets para mãos premium e bluffs fortes.",
        explanationEn: "4-betting AJo is too loose. Save your 4-bets for premium hands and strong bluffs.",
        explanationPt: "4-bet com AJo é muito solto. Reserve seus 4-bets para mãos premium e bluffs fortes."
      }
    ]),
    correctAction: "fold",
    equity: 0.42,
    frequency: 0.70,
    explanation: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão e evita jogar um spot difícil fora de posição.",
    explanationEn: "AJo is at the bottom of your calling range vs CO 3-bet. Folding is standard and prevents playing a difficult spot out of position.",
    explanationPt: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão e evita jogar um spot difícil fora de posição.",
    context: JSON.stringify({
      effectiveStacks: 182,
      potOdds: 0.43,
      hasAggression: true,
      lastAggressor: "CO",
      boardTexture: "preflop",
      handStrength: "medium"
    }),
    difficulty: "advanced",
    category: "3bet_defense",
    tags: JSON.stringify(["preflop", "3bet_defense", "position_disadvantage", "marginal_hand"])
  },

  {
    id: "scenario_003_river_bluff_catch",
    name: "River Bluff Catch Decision",
    nameEn: "River Bluff Catch Decision",
    namePt: "Decisão de Bluff Catch no River",
    description: "Facing large river bet with second set - bluff catch spot",
    descriptionEn: "Facing large river bet with second set - bluff catch spot",
    descriptionPt: "Enfrentando aposta grande no river com trinca - spot de bluff catch",
    gameType: "cash",
    street: "river",
    position: "BB",
    stackDepth: "medium",
    playerCards: JSON.stringify({ card1: "Jh", card2: "Js" }),
    communityCards: JSON.stringify({ 
      flop: ["Kc", "Jd", "7s"], 
      turn: "4h", 
      river: "Ac" 
    }),
    potSize: 240.0,
    playerStack: 80.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 120, isActive: true, stackSize: 80 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.35,
        explanation: "Foldar trinca de valetes é muito apertado. Oponente pode ter muitos bluffs neste runout.",
        explanationEn: "Folding trips is too tight. Opponent can have many bluffs on this runout.",
        explanationPt: "Foldar trinca de valetes é muito apertado. Oponente pode ter muitos bluffs neste runout."
      },
      {
        action: "call",
        amount: 120,
        isCorrect: true,
        frequency: 0.65,
        explanation: "Excelente bluff catch! Você tem trinca de valetes e o range do oponente inclui muitos draws perdidos e bluffs. O Ás no river é uma carta perfeita de bluff.",
        explanationEn: "Great bluff catch! You have trips and opponent's range includes many missed draws and bluffs. The river Ace is a perfect bluff card for them.",
        explanationPt: "Excelente bluff catch! Você tem trinca de valetes e o range do oponente inclui muitos draws perdidos e bluffs. O Ás no river é uma carta perfeita de bluff."
      }
    ]),
    correctAction: "call",
    equity: 0.55,
    frequency: 0.65,
    explanation: "Excelente bluff catch! Você tem trinca de valetes e o range do oponente inclui muitos draws perdidos e bluffs. O Ás no river é uma carta perfeita de bluff.",
    explanationEn: "Great bluff catch! You have trips and opponent's range includes many missed draws and bluffs. The river Ace is a perfect bluff card for them.",
    explanationPt: "Excelente bluff catch! Você tem trinca de valetes e o range do oponente inclui muitos draws perdidos e bluffs. O Ás no river é uma carta perfeita de bluff.",
    context: JSON.stringify({
      effectiveStacks: 80,
      potOdds: 0.50,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "coordinated",
      handStrength: "strong"
    }),
    difficulty: "advanced",
    category: "river_decisions",
    tags: JSON.stringify(["bluff_catch", "river_play", "trips", "large_bet"])
  },

  {
    id: "scenario_004_co_squeeze_spot",
    name: "CO Squeeze vs MP Open + BTN Call",
    nameEn: "CO Squeeze vs MP Open + BTN Call",
    namePt: "CO Squeeze vs Abertura MP + Call BTN",
    description: "Cutoff squeeze opportunity with A5s after MP opens and BTN calls",
    descriptionEn: "Cutoff squeeze opportunity with A5s after MP opens and BTN calls",
    descriptionPt: "Oportunidade de squeeze do CO com A5s após MP abrir e BTN pagar",
    gameType: "cash",
    street: "preflop",
    position: "CO",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "As", card2: "5s" }),
    communityCards: JSON.stringify({}),
    potSize: 15.0,
    playerStack: 194.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "raise", amount: 6, isActive: true, stackSize: 194 },
      { position: "BTN", action: "call", amount: 6, isActive: true, stackSize: 194 },
      { position: "SB", action: "fold", isActive: false, stackSize: 199 },
      { position: "BB", action: "fold", isActive: false, stackSize: 198 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.60,
        explanation: "Fold é seguro mas desperdiça uma boa oportunidade de squeeze. A5s tem boa jogabilidade.",
        explanationEn: "Folding is safe but wastes a good squeeze opportunity. A5s has good playability.",
        explanationPt: "Fold é seguro mas desperdiça uma boa oportunidade de squeeze. A5s tem boa jogabilidade."
      },
      {
        action: "call",
        amount: 6,
        isCorrect: false,
        frequency: 0.15,
        explanation: "Call cria um pote multiway fora de posição. Squeeze ou fold são melhores opções.",
        explanationEn: "Calling creates a multiway pot out of position. Squeeze or fold are better options.",
        explanationPt: "Call cria um pote multiway fora de posição. Squeeze ou fold são melhores opções."
      },
      {
        action: "raise",
        amount: 22,
        sizing: "large",
        isCorrect: true,
        frequency: 0.25,
        explanation: "Squeeze perfeito! A5s é ideal para squeeze - tem blockers de Ás, boa jogabilidade pós-flop, e pode fazer os dois oponentes foldarem frequentemente.",
        explanationEn: "Perfect squeeze! A5s is ideal for squeezing - has ace blockers, good postflop playability, and can make both opponents fold frequently.",
        explanationPt: "Squeeze perfeito! A5s é ideal para squeeze - tem blockers de Ás, boa jogabilidade pós-flop, e pode fazer os dois oponentes foldarem frequentemente."
      }
    ]),
    correctAction: "raise",
    equity: 0.38,
    frequency: 0.25,
    explanation: "Squeeze perfeito! A5s é ideal para squeeze - tem blockers de Ás, boa jogabilidade pós-flop, e pode fazer os dois oponentes foldarem frequentemente.",
    explanationEn: "Perfect squeeze! A5s is ideal for squeezing - has ace blockers, good postflop playability, and can make both opponents fold frequently.",
    explanationPt: "Squeeze perfeito! A5s é ideal para squeeze - tem blockers de Ás, boa jogabilidade pós-flop, e pode fazer os dois oponentes foldarem frequentemente.",
    context: JSON.stringify({
      effectiveStacks: 194,
      potOdds: 0.27,
      hasAggression: true,
      lastAggressor: "MP",
      boardTexture: "preflop",
      handStrength: "medium"
    }),
    difficulty: "advanced",
    category: "squeeze_spots",
    tags: JSON.stringify(["preflop", "squeeze", "suited_ace", "position_play"])
  },

  {
    id: "scenario_005_turn_check_raise",
    name: "Turn Check-Raise Bluff",
    nameEn: "Turn Check-Raise Bluff",
    namePt: "Check-Raise Bluff no Turn",
    description: "Check-raise bluff opportunity on turn with combo draw",
    descriptionEn: "Check-raise bluff opportunity on turn with combo draw",
    descriptionPt: "Oportunidade de check-raise bluff no turn com combo draw",
    gameType: "cash",
    street: "turn",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "9h", card2: "8h" }),
    communityCards: JSON.stringify({ 
      flop: ["7h", "6c", "2d"], 
      turn: "5s"
    }),
    potSize: 45.0,
    playerStack: 170.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 30, isActive: true, stackSize: 164 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.10,
        explanation: "Fold desperdiça uma mão com muita equidade. Você tem straight draw e overcards.",
        explanationEn: "Folding wastes a hand with lots of equity. You have straight draw and overcards.",
        explanationPt: "Fold desperdiça uma mão com muita equidade. Você tem straight draw e overcards."
      },
      {
        action: "call",
        amount: 30,
        isCorrect: false,
        frequency: 0.50,
        explanation: "Call é passivo. Com tanto equity, você deve ser mais agressivo e aplicar pressão.",
        explanationEn: "Calling is passive. With so much equity, you should be more aggressive and apply pressure.",
        explanationPt: "Call é passivo. Com tanto equity, você deve ser mais agressivo e aplicar pressão."
      },
      {
        action: "raise",
        amount: 90,
        sizing: "large",
        isCorrect: true,
        frequency: 0.40,
        explanation: "Check-raise perfeito! Você tem 13 outs (straight + overcards), pode fazer o oponente foldar, e se pago ainda tem boa equidade para o river.",
        explanationEn: "Perfect check-raise! You have 13 outs (straight + overcards), can make opponent fold, and if called still have good equity for the river.",
        explanationPt: "Check-raise perfeito! Você tem 13 outs (straight + overcards), pode fazer o oponente foldar, e se pago ainda tem boa equidade para o river."
      }
    ]),
    correctAction: "raise",
    equity: 0.52,
    frequency: 0.40,
    explanation: "Check-raise perfeito! Você tem 13 outs (straight + overcards), pode fazer o oponente foldar, e se pago ainda tem boa equidade para o river.",
    explanationEn: "Perfect check-raise! You have 13 outs (straight + overcards), can make opponent fold, and if called still have good equity for the river.",
    explanationPt: "Check-raise perfeito! Você tem 13 outs (straight + overcards), pode fazer o oponente foldar, e se pago ainda tem boa equidade para o river.",
    context: JSON.stringify({
      effectiveStacks: 164,
      potOdds: 0.33,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "coordinated",
      handStrength: "draw"
    }),
    difficulty: "intermediate",
    category: "check_raise",
    tags: JSON.stringify(["turn_play", "check_raise", "combo_draw", "semi_bluff"])
  }
]

// Exemplo de situação real para o banco de dados
export const exampleGTOScenarios: GTOScenarioData[] = [
    {
        id: "scenario_001_btn_cbet_dry_board",
        name: "BTN C-bet on Dry Board",
        nameEn: "BTN C-bet on Dry Board",
        namePt: "C-bet do BTN em Board Seco",
        description: "Button continuation bet on A-7-2 rainbow after opening preflop",
        descriptionEn: "Button continuation bet on A-7-2 rainbow after opening preflop",
        descriptionPt: "Continuation bet do Button em A-7-2 rainbow após abertura pré-flop",

        gameType: "cash",
        street: "flop",
        blinds: {
            smallBlind: 1,
            bigBlind: 2
        },

        playerCards: {
            card1: "Kh", // Rei de copas
            card2: "Qd"  // Dama de ouros
        },
        communityCards: {
            flop: ["As", "7c", "2h"] // Ás-7-2 rainbow
        },

        playerPosition: "BTN",
        playerStack: 200,

        opponentActions: [
            {
                position: "UTG",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "MP",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "CO",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "SB",
                action: "fold",
                stackBefore: 199,
                stackAfter: 199,
                isActive: false
            },
            {
                position: "BB",
                action: "call",
                amount: 6,
                stackBefore: 198,
                stackAfter: 192,
                isActive: true
            }
        ],

        potSize: 15, // SB (1) + BB (2) + BTN raise (6) + BB call (6)

        availableActions: [
            {
                action: "fold",
                isCorrect: false,
                frequency: 0.05,
                explanation: "Folding here would be extremely weak. You have overcards and position.",
                explanationEn: "Folding here would be extremely weak. You have overcards and position.",
                explanationPt: "Desistir aqui seria extremamente fraco. Você tem overcards e posição."
            },
            {
                action: "check",
                isCorrect: false,
                frequency: 0.15,
                explanation: "Checking is passive. You should bet for value and protection.",
                explanationEn: "Checking is passive. You should bet for value and protection.",
                explanationPt: "Check é passivo. Você deve apostar por valor e proteção."
            },
            {
                action: "bet",
                amount: 10,
                sizing: "medium",
                isCorrect: true,
                frequency: 0.80,
                equity: 0.35,
                explanation: "Perfect c-bet spot! You have overcards, position, and this dry board favors your range. Bet 2/3 pot for value and to fold out worse hands.",
                explanationEn: "Perfect c-bet spot! You have overcards, position, and this dry board favors your range. Bet 2/3 pot for value and to fold out worse hands.",
                explanationPt: "Spot perfeito para c-bet! Você tem overcards, posição, e este board seco favorece seu range. Aposte 2/3 do pot por valor e para foldar mãos piores."
            }
        ],

        context: {
            effectiveStacks: 192,
            potOdds: 0.67, // 10 to win 15
            hasAggression: true,
            lastAggressor: "BTN",
            boardTexture: "dry",
            handStrength: "medium"
        },

        difficulty: "intermediate",
        category: "cbet_spots",
        tags: ["position_play", "continuation_bet", "dry_board", "overcards"]
    },

    {
        id: "scenario_002_bb_vs_3bet_decision",
        name: "BB vs CO 3-bet with AJo",
        nameEn: "BB vs CO 3-bet with AJo",
        namePt: "BB vs 3-bet do CO com AJo",
        description: "Big blind facing 3-bet from cutoff with AJ offsuit",
        descriptionEn: "Big blind facing 3-bet from cutoff with AJ offsuit",
        descriptionPt: "Big blind enfrentando 3-bet do cutoff com AJ offsuit",

        gameType: "cash",
        street: "preflop",
        blinds: {
            smallBlind: 1,
            bigBlind: 2
        },

        playerCards: {
            card1: "Ah",
            card2: "Jc"
        },
        communityCards: {},

        playerPosition: "BB",
        playerStack: 200,

        opponentActions: [
            {
                position: "UTG",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "MP",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "CO",
                action: "raise",
                amount: 18, // 3-bet to 18
                stackBefore: 200,
                stackAfter: 182,
                isActive: true
            },
            {
                position: "BTN",
                action: "fold",
                stackBefore: 200,
                stackAfter: 200,
                isActive: false
            },
            {
                position: "SB",
                action: "fold",
                stackBefore: 199,
                stackAfter: 199,
                isActive: false
            }
        ],

        potSize: 37, // SB (1) + BB (2) + original raise (6) + 3-bet (18) + BB call (16)

        availableActions: [
            {
                action: "fold",
                isCorrect: true,
                frequency: 0.70,
                explanation: "AJo is at the bottom of your calling range vs CO 3-bet. Folding is standard and prevents playing a difficult spot out of position.",
                explanationEn: "AJo is at the bottom of your calling range vs CO 3-bet. Folding is standard and prevents playing a difficult spot out of position.",
                explanationPt: "AJo está no fundo do seu range de call vs 3-bet do CO. Fold é padrão e evita jogar um spot difícil fora de posição."
            },
            {
                action: "call",
                amount: 16,
                isCorrect: false,
                frequency: 0.25,
                equity: 0.42,
                explanation: "Calling is marginal. You're getting good odds but will be out of position postflop with a hand that doesn't play well.",
                explanationEn: "Calling is marginal. You're getting good odds but will be out of position postflop with a hand that doesn't play well.",
                explanationPt: "Call é marginal. Você está recebendo boas odds mas ficará fora de posição no pós-flop com uma mão que não joga bem."
            },
            {
                action: "raise",
                amount: 54, // 4-bet
                sizing: "large",
                isCorrect: false,
                frequency: 0.05,
                explanation: "4-betting AJo is too loose. Save your 4-bets for premium hands and strong bluffs.",
                explanationEn: "4-betting AJo is too loose. Save your 4-bets for premium hands and strong bluffs.",
                explanationPt: "4-bet com AJo é muito solto. Reserve seus 4-bets para mãos premium e bluffs fortes."
            }
        ],

        context: {
            effectiveStacks: 182,
            potOdds: 0.43, // 16 to win 21
            hasAggression: true,
            lastAggressor: "CO",
            boardTexture: "rainbow", // preflop
            handStrength: "medium"
        },

        difficulty: "advanced",
        category: "3bet_pots",
        tags: ["preflop", "3bet_defense", "position_disadvantage", "marginal_hand"]
    },

    {
        id: "scenario_003_river_bluff_catch",
        name: "River Bluff Catch Decision",
        nameEn: "River Bluff Catch Decision",
        namePt: "Decisão de Bluff Catch no River",
        description: "Facing large river bet with second pair - bluff catch spot",
        descriptionEn: "Facing large river bet with second pair - bluff catch spot",
        descriptionPt: "Enfrentando aposta grande no river com segundo par - spot de bluff catch",

        gameType: "cash",
        street: "river",
        blinds: {
            smallBlind: 1,
            bigBlind: 2
        },

        playerCards: {
            card1: "Jh",
            card2: "Js"
        },
        communityCards: {
            flop: ["Kc", "J♦", "7s"],
            turn: "4h",
            river: "Ac"
        },

        playerPosition: "BB",
        playerStack: 180,

        opponentActions: [
            {
                position: "BTN",
                action: "bet",
                amount: 120, // Large river bet
                stackBefore: 200,
                stackAfter: 80,
                isActive: true
            }
        ],

        potSize: 240, // Previous action + current bet

        availableActions: [
            {
                action: "fold",
                isCorrect: false,
                frequency: 0.35,
                explanation: "Folding second set is too tight. Opponent can have many bluffs on this runout.",
                explanationEn: "Folding second set is too tight. Opponent can have many bluffs on this runout.",
                explanationPt: "Foldar trinca de valetes é muito apertado. Oponente pode ter muitos bluffs neste runout."
            },
            {
                action: "call",
                amount: 120,
                isCorrect: true,
                frequency: 0.65,
                equity: 0.55,
                explanation: "Great bluff catch! You have second set and opponent's range includes many missed draws and bluffs. The river Ace is a perfect bluff card for them.",
                explanationEn: "Great bluff catch! You have second set and opponent's range includes many missed draws and bluffs. The river Ace is a perfect bluff card for them.",
                explanationPt: "Excelente bluff catch! Você tem trinca de valetes e o range do oponente inclui muitos draws perdidos e bluffs. O Ás no river é uma carta perfeita de bluff para eles."
            }
        ],

        context: {
            effectiveStacks: 80,
            potOdds: 0.50, // 120 to win 240
            hasAggression: true,
            lastAggressor: "BTN",
            boardTexture: "coordinated",
            handStrength: "strong"
        },

        difficulty: "advanced",
        category: "river_decisions",
        tags: ["bluff_catch", "river_play", "second_set", "large_bet"]
    }
]

// Função para converter o modelo para o formato do banco de dados
export function convertToDBFormat(scenario: GTOScenarioData) {
    return {
        name: scenario.name,
        nameEn: scenario.nameEn,
        namePt: scenario.namePt,
        description: scenario.description,
        descriptionEn: scenario.descriptionEn,
        descriptionPt: scenario.descriptionPt,
        gameType: scenario.gameType,
        street: scenario.street,
        position: scenario.playerPosition,
        stackDepth: scenario.context.effectiveStacks > 150 ? 'deep' : scenario.context.effectiveStacks > 50 ? 'medium' : 'shallow',
        action: scenario.availableActions.find(a => a.isCorrect)?.action || 'fold',
        betSizing: JSON.stringify({
            small: 0.33,
            medium: 0.66,
            large: 1.0,
            overbet: 1.5
        }),
        range: JSON.stringify([`${scenario.playerCards.card1}${scenario.playerCards.card2}`]),
        equity: scenario.availableActions.find(a => a.isCorrect)?.equity || 0.5,
        frequency: scenario.availableActions.find(a => a.isCorrect)?.frequency || 0.5,
        explanation: scenario.availableActions.find(a => a.isCorrect)?.explanation || '',
        explanationEn: scenario.availableActions.find(a => a.isCorrect)?.explanationEn || '',
        explanationPt: scenario.availableActions.find(a => a.isCorrect)?.explanationPt || '',
        difficulty: scenario.difficulty,
        category: scenario.category
    }
}
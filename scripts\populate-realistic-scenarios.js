const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

// Cenários GTO 100% realistas e coerentes baseados na teoria atual
const realisticGTOScenarios = [
  // === PREFLOP FUNDAMENTALS ===
  {
    name: "UTG RFI with AQo",
    nameEn: "UTG RFI with AQo",
    namePt: "Abertura UTG com AQo",
    description: "First to act UTG with AQo in 6-max cash game",
    descriptionEn: "First to act UTG with AQo in 6-max cash game",
    descriptionPt: "Primeiro a agir UTG com AQo em cash game 6-max",
    gameType: "cash",
    street: "preflop",
    position: "UTG",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "hearts", rank: "A", value: 14 },
      { suit: "clubs", rank: "Q", value: 12 }
    ]),
    communityCards: JSON.stringify([]),
    potSize: 1.5,
    playerStack: 100.0,
    opponentActions: JSON.stringify([
      { position: "MP", action: "pending", isActive: true, stackSize: 100 },
      { position: "CO", action: "pending", isActive: true, stackSize: 100 },
      { position: "BTN", action: "pending", isActive: true, stackSize: 100 },
      { position: "SB", action: "pending", isActive: true, stackSize: 99.5 },
      { position: "BB", action: "pending", isActive: true, stackSize: 99 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.05,
        explanation: "AQo é muito forte para fold UTG",
        explanationEn: "AQo is too strong to fold UTG",
        explanationPt: "AQo é muito forte para fold UTG"
      },
      {
        action: "raise",
        amount: 2.5,
        sizing: "standard",
        isCorrect: true,
        frequency: 0.95,
        explanation: "AQo está no topo do range de abertura UTG",
        explanationEn: "AQo is at the top of UTG opening range",
        explanationPt: "AQo está no topo do range de abertura UTG"
      }
    ]),
    correctAction: "raise",
    equity: 0.31,
    frequency: 0.95,
    explanation: "AQo é uma das melhores mãos para abrir UTG. Representa ~3% do range de abertura mais forte.",
    explanationEn: "AQo is one of the best hands to open UTG. Represents ~3% of the strongest opening range.",
    explanationPt: "AQo é uma das melhores mãos para abrir UTG. Representa ~3% do range de abertura mais forte.",
    context: JSON.stringify({
      spr: 40,
      boardTexture: "preflop",
      handStrength: "premium",
      position: "early"
    }),
    difficulty: "beginner",
    category: "preflop_opening",
    tags: JSON.stringify(["preflop", "opening", "utg", "premium_hands"])
  },

  {
    name: "BB vs UTG 3-bet with AKs",
    nameEn: "BB vs UTG 3-bet with AKs",
    namePt: "BB vs UTG 3-bet com AKs",
    description: "UTG opened 2.5bb, action folds to BB with AKs",
    descriptionEn: "UTG opened 2.5bb, action folds to BB with AKs",
    descriptionPt: "UTG abriu 2.5bb, ação fold até BB com AKs",
    gameType: "cash",
    street: "preflop",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "spades", rank: "A", value: 14 },
      { suit: "spades", rank: "K", value: 13 }
    ]),
    communityCards: JSON.stringify([]),
    potSize: 4.0,
    playerStack: 99.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "raise", amount: 2.5, isActive: true, stackSize: 97.5 },
      { position: "MP", action: "fold", isActive: false, stackSize: 100 },
      { position: "CO", action: "fold", isActive: false, stackSize: 100 },
      { position: "BTN", action: "fold", isActive: false, stackSize: 100 },
      { position: "SB", action: "fold", isActive: false, stackSize: 99.5 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.02,
        explanation: "AKs nunca deve ser foldado contra abertura UTG",
        explanationEn: "AKs should never be folded against UTG open",
        explanationPt: "AKs nunca deve ser foldado contra abertura UTG"
      },
      {
        action: "call",
        amount: 1.5,
        isCorrect: false,
        frequency: 0.23,
        explanation: "Call é muito passivo com AKs",
        explanationEn: "Call is too passive with AKs",
        explanationPt: "Call é muito passivo com AKs"
      },
      {
        action: "raise",
        amount: 9.0,
        sizing: "standard",
        isCorrect: true,
        frequency: 0.75,
        explanation: "3-bet com AKs para valor e proteção",
        explanationEn: "3-bet with AKs for value and protection",
        explanationPt: "3-bet com AKs para valor e proteção"
      }
    ]),
    correctAction: "raise",
    equity: 0.46,
    frequency: 0.75,
    explanation: "AKs é premium e deve 3-bet na maioria das vezes contra UTG. Temos equity excelente e queremos construir o pote.",
    explanationEn: "AKs is premium and should 3-bet most of the time against UTG. We have excellent equity and want to build the pot.",
    explanationPt: "AKs é premium e deve 3-bet na maioria das vezes contra UTG. Temos equity excelente e queremos construir o pote.",
    context: JSON.stringify({
      spr: 11,
      boardTexture: "preflop",
      handStrength: "premium",
      position: "bb_vs_utg"
    }),
    difficulty: "beginner",
    category: "3bet_spots",
    tags: JSON.stringify(["preflop", "3bet", "bb_defense", "premium"])
  },

  {
    name: "BTN vs SB 3-bet with 22",
    nameEn: "BTN vs SB 3-bet with 22",
    namePt: "BTN vs SB 3-bet com 22",
    description: "Opened BTN, SB 3-bet to 10bb, holding pocket deuces",
    descriptionEn: "Opened BTN, SB 3-bet to 10bb, holding pocket deuces",
    descriptionPt: "Abriu BTN, SB 3-bet para 10bb, segurando par de 2",
    gameType: "cash",
    street: "preflop",
    position: "BTN",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "hearts", rank: "2", value: 2 },
      { suit: "clubs", rank: "2", value: 2 }
    ]),
    communityCards: JSON.stringify([]),
    potSize: 12.5,
    playerStack: 97.5,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 100 },
      { position: "MP", action: "fold", isActive: false, stackSize: 100 },
      { position: "CO", action: "fold", isActive: false, stackSize: 100 },
      { position: "SB", action: "raise", amount: 10, isActive: true, stackSize: 90 },
      { position: "BB", action: "fold", isActive: false, stackSize: 98 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: true,
        frequency: 0.85,
        explanation: "22 não tem equity suficiente contra range de 3-bet do SB",
        explanationEn: "22 doesn't have enough equity against SB's 3-bet range",
        explanationPt: "22 não tem equity suficiente contra range de 3-bet do SB"
      },
      {
        action: "call",
        amount: 7.5,
        isCorrect: false,
        frequency: 0.15,
        explanation: "Call com 22 é marginal devido às pot odds ruins",
        explanationEn: "Call with 22 is marginal due to poor pot odds",
        explanationPt: "Call com 22 é marginal devido às pot odds ruins"
      },
      {
        action: "raise",
        amount: 25,
        sizing: "large",
        isCorrect: false,
        frequency: 0.0,
        explanation: "4-bet com 22 é muito especulativo",
        explanationEn: "4-bet with 22 is too speculative",
        explanationPt: "4-bet com 22 é muito especulativo"
      }
    ]),
    correctAction: "fold",
    equity: 0.35,
    frequency: 0.85,
    explanation: "Pares pequenos são difíceis de jogar contra 3-bet fora de posição. SB tem range forte e não temos implied odds suficientes.",
    explanationEn: "Small pairs are difficult to play against 3-bet out of position. SB has strong range and we don't have sufficient implied odds.",
    explanationPt: "Pares pequenos são difíceis de jogar contra 3-bet fora de posição. SB tem range forte e não temos implied odds suficientes.",
    context: JSON.stringify({
      spr: 9,
      boardTexture: "preflop",
      handStrength: "speculative",
      position: "btn_vs_sb"
    }),
    difficulty: "intermediate",
    category: "3bet_defense",
    tags: JSON.stringify(["preflop", "3bet_defense", "small_pairs", "fold"])
  },

  // === POSTFLOP C-BETTING ===
  {
    name: "Dry Board C-bet with AK",
    nameEn: "Dry Board C-bet with AK",
    namePt: "C-bet em Board Seco com AK",
    description: "Opened UTG, BB called. Flop A♠7♣2♦ with AK",
    descriptionEn: "Opened UTG, BB called. Flop A♠7♣2♦ with AK",
    descriptionPt: "Abriu UTG, BB pagou. Flop A♠7♣2♦ com AK",
    gameType: "cash",
    street: "flop",
    position: "UTG",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "hearts", rank: "A", value: 14 },
      { suit: "diamonds", rank: "K", value: 13 }
    ]),
    communityCards: JSON.stringify([
      { suit: "spades", rank: "A", value: 14 },
      { suit: "clubs", rank: "7", value: 7 },
      { suit: "diamonds", rank: "2", value: 2 }
    ]),
    potSize: 5.0,
    playerStack: 95.0,
    opponentActions: JSON.stringify([
      { position: "BB", action: "check", isActive: true, stackSize: 97.5 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "check",
        isCorrect: false,
        frequency: 0.05,
        explanation: "Check desperdiça valor com top pair top kicker",
        explanationEn: "Check wastes value with top pair top kicker",
        explanationPt: "Check desperdiça valor com top pair top kicker"
      },
      {
        action: "bet",
        amount: 3.5,
        sizing: "standard",
        isCorrect: true,
        frequency: 0.95,
        explanation: "Bet para valor em board que favorece nosso range",
        explanationEn: "Bet for value on board that favors our range",
        explanationPt: "Bet para valor em board que favorece nosso range"
      }
    ]),
    correctAction: "bet",
    equity: 0.87,
    frequency: 0.95,
    explanation: "AK em A-7-2 é clara aposta para valor. Board seco favorece muito o range do opener UTG e temos uma das melhores mãos possíveis.",
    explanationEn: "AK on A-7-2 is clear value bet. Dry board heavily favors UTG opener's range and we have one of the best possible hands.",
    explanationPt: "AK em A-7-2 é clara aposta para valor. Board seco favorece muito o range do opener UTG e temos uma das melhores mãos possíveis.",
    context: JSON.stringify({
      spr: 19,
      boardTexture: "dry",
      handStrength: "strong",
      cbet_frequency: 0.75
    }),
    difficulty: "beginner",
    category: "cbet_spots",
    tags: JSON.stringify(["postflop", "cbet", "value", "dry_board"])
  },

  {
    name: "Connected Board Check with AQ",
    nameEn: "Connected Board Check with AQ",
    namePt: "Check em Board Conectado com AQ",
    description: "Opened BTN, BB called. Flop 9♠8♥6♣ with AQ",
    descriptionEn: "Opened BTN, BB called. Flop 9♠8♥6♣ with AQ",
    descriptionPt: "Abriu BTN, BB pagou. Flop 9♠8♥6♣ com AQ",
    gameType: "cash",
    street: "flop",
    position: "BTN",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "diamonds", rank: "A", value: 14 },
      { suit: "clubs", rank: "Q", value: 12 }
    ]),
    communityCards: JSON.stringify([
      { suit: "spades", rank: "9", value: 9 },
      { suit: "hearts", rank: "8", value: 8 },
      { suit: "clubs", rank: "6", value: 6 }
    ]),
    potSize: 5.0,
    playerStack: 95.0,
    opponentActions: JSON.stringify([
      { position: "BB", action: "check", isActive: true, stackSize: 97.5 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "check",
        isCorrect: true,
        frequency: 0.70,
        explanation: "Check-back com AQ em board conectado que não favorece nosso range",
        explanationEn: "Check-back with AQ on connected board that doesn't favor our range",
        explanationPt: "Check-back com AQ em board conectado que não favorece nosso range"
      },
      {
        action: "bet",
        amount: 3.5,
        sizing: "standard",
        isCorrect: false,
        frequency: 0.30,
        explanation: "Bet é marginal - board conectado favorece o caller",
        explanationEn: "Bet is marginal - connected board favors the caller",
        explanationPt: "Bet é marginal - board conectado favorece o caller"
      }
    ]),
    correctAction: "check",
    equity: 0.28,
    frequency: 0.70,
    explanation: "AQ em 9-8-6 tem pouca equity e board conectado favorece o range do BB. Melhor controlar pot size e tentar realizar equity.",
    explanationEn: "AQ on 9-8-6 has little equity and connected board favors BB's range. Better to control pot size and try to realize equity.",
    explanationPt: "AQ em 9-8-6 tem pouca equity e board conectado favorece o range do BB. Melhor controlar pot size e tentar realizar equity.",
    context: JSON.stringify({
      spr: 19,
      boardTexture: "connected",
      handStrength: "weak",
      cbet_frequency: 0.35
    }),
    difficulty: "intermediate",
    category: "cbet_spots",
    tags: JSON.stringify(["postflop", "check_back", "connected_board", "pot_control"])
  },

  // === TURN PLAY ===
  {
    name: "Turn Barrel with Flush Draw",
    nameEn: "Turn Barrel with Flush Draw",
    namePt: "Barrel no Turn com Flush Draw",
    description: "C-bet flop, called. Turn brings flush draw with A♠Q♠",
    descriptionEn: "C-bet flop, called. Turn brings flush draw with A♠Q♠",
    descriptionPt: "C-bet no flop, pago. Turn traz flush draw com A♠Q♠",
    gameType: "cash",
    street: "turn",
    position: "CO",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "spades", rank: "A", value: 14 },
      { suit: "spades", rank: "Q", value: 12 }
    ]),
    communityCards: JSON.stringify([
      { suit: "hearts", rank: "K", value: 13 },
      { suit: "clubs", rank: "9", value: 9 },
      { suit: "diamonds", rank: "4", value: 4 },
      { suit: "spades", rank: "7", value: 7 }
    ]),
    potSize: 11.0,
    playerStack: 87.0,
    opponentActions: JSON.stringify([
      { position: "BB", action: "check", isActive: true, stackSize: 89.5 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "check",
        isCorrect: false,
        frequency: 0.25,
        explanation: "Check desperdiça equity com nut flush draw + overcard",
        explanationEn: "Check wastes equity with nut flush draw + overcard",
        explanationPt: "Check desperdiça equity com nut flush draw + overcard"
      },
      {
        action: "bet",
        amount: 8.0,
        sizing: "standard",
        isCorrect: true,
        frequency: 0.75,
        explanation: "Bet para semi-bluff com 12 outs limpos",
        explanationEn: "Bet to semi-bluff with 12 clean outs",
        explanationPt: "Bet para semi-bluff com 12 outs limpos"
      }
    ]),
    correctAction: "bet",
    equity: 0.42,
    frequency: 0.75,
    explanation: "A♠Q♠ tem nut flush draw + overcard = 12 outs. Com ~42% equity, devemos apostar para semi-bluff e aplicar pressão.",
    explanationEn: "A♠Q♠ has nut flush draw + overcard = 12 outs. With ~42% equity, we should bet to semi-bluff and apply pressure.",
    explanationPt: "A♠Q♠ tem nut flush draw + overcard = 12 outs. Com ~42% equity, devemos apostar para semi-bluff e aplicar pressão.",
    context: JSON.stringify({
      spr: 8,
      boardTexture: "dynamic",
      handStrength: "draw",
      outs: 12
    }),
    difficulty: "intermediate",
    category: "turn_play",
    tags: JSON.stringify(["turn", "semi_bluff", "flush_draw", "barrel"])
  },

  // === RIVER DECISIONS ===
  {
    name: "River Bluff Catch with Second Pair",
    nameEn: "River Bluff Catch with Second Pair",
    namePt: "Bluff Catch no River com Segunda Dupla",
    description: "River bet 18bb into 25bb pot. Board Q♠J♥8♣4♦2♠, holding QT",
    descriptionEn: "River bet 18bb into 25bb pot. Board Q♠J♥8♣4♦2♠, holding QT",
    descriptionPt: "Aposta de 18bb no river em pot de 25bb. Board Q♠J♥8♣4♦2♠, segurando QT",
    gameType: "cash",
    street: "river",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "diamonds", rank: "Q", value: 12 },
      { suit: "hearts", rank: "T", value: 10 }
    ]),
    communityCards: JSON.stringify([
      { suit: "spades", rank: "Q", value: 12 },
      { suit: "hearts", rank: "J", value: 11 },
      { suit: "clubs", rank: "8", value: 8 },
      { suit: "diamonds", rank: "4", value: 4 },
      { suit: "spades", rank: "2", value: 2 }
    ]),
    potSize: 43.0,
    playerStack: 57.0,
    opponentActions: JSON.stringify([
      { position: "CO", action: "bet", amount: 18, isActive: true, stackSize: 57 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.35,
        explanation: "Fold é muito tight contra esta bet size",
        explanationEn: "Fold is too tight against this bet size",
        explanationPt: "Fold é muito tight contra esta bet size"
      },
      {
        action: "call",
        amount: 18,
        isCorrect: true,
        frequency: 0.65,
        explanation: "Call com bluff catcher - pot odds favoráveis",
        explanationEn: "Call with bluff catcher - favorable pot odds",
        explanationPt: "Call com bluff catcher - pot odds favoráveis"
      }
    ]),
    correctAction: "call",
    equity: 0.58,
    frequency: 0.65,
    explanation: "QT é bluff catcher clássico. Com pot odds de 2.4:1, precisamos ganhar ~30% das vezes. Villain pode bluffar com draws perdidos.",
    explanationEn: "QT is classic bluff catcher. With 2.4:1 pot odds, we need to win ~30% of the time. Villain can bluff with missed draws.",
    explanationPt: "QT é bluff catcher clássico. Com pot odds de 2.4:1, precisamos ganhar ~30% das vezes. Villain pode bluffar com draws perdidos.",
    context: JSON.stringify({
      spr: 1.3,
      boardTexture: "static",
      handStrength: "bluff_catcher",
      pot_odds: 2.4
    }),
    difficulty: "advanced",
    category: "bluff_catching",
    tags: JSON.stringify(["river", "bluff_catch", "pot_odds", "decision"])
  },

  // === MULTIWAY POTS ===
  {
    name: "Multiway C-bet with Overpair",
    nameEn: "Multiway C-bet with Overpair",
    namePt: "C-bet Multiway com Overpair",
    description: "Opened UTG, MP and BB called. Flop 8♠6♣3♦ with JJ",
    descriptionEn: "Opened UTG, MP and BB called. Flop 8♠6♣3♦ with JJ",
    descriptionPt: "Abriu UTG, MP e BB pagaram. Flop 8♠6♣3♦ com JJ",
    gameType: "cash",
    street: "flop",
    position: "UTG",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "hearts", rank: "J", value: 11 },
      { suit: "clubs", rank: "J", value: 11 }
    ]),
    communityCards: JSON.stringify([
      { suit: "spades", rank: "8", value: 8 },
      { suit: "clubs", rank: "6", value: 6 },
      { suit: "diamonds", rank: "3", value: 3 }
    ]),
    potSize: 7.5,
    playerStack: 95.0,
    opponentActions: JSON.stringify([
      { position: "MP", action: "check", isActive: true, stackSize: 97.5 },
      { position: "BB", action: "check", isActive: true, stackSize: 97.5 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "check",
        isCorrect: false,
        frequency: 0.15,
        explanation: "Check desperdiça valor com overpair em board seco",
        explanationEn: "Check wastes value with overpair on dry board",
        explanationPt: "Check desperdiça valor com overpair em board seco"
      },
      {
        action: "bet",
        amount: 4.0,
        sizing: "small",
        isCorrect: true,
        frequency: 0.85,
        explanation: "Bet pequeno para extrair valor de múltiplos oponentes",
        explanationEn: "Small bet to extract value from multiple opponents",
        explanationPt: "Bet pequeno para extrair valor de múltiplos oponentes"
      }
    ]),
    correctAction: "bet",
    equity: 0.78,
    frequency: 0.85,
    explanation: "JJ é overpair forte em 8-6-3. Em potes multiway, usamos bet size menor (~50% pot) para manter mais mãos no pote e extrair valor.",
    explanationEn: "JJ is strong overpair on 8-6-3. In multiway pots, we use smaller bet size (~50% pot) to keep more hands in and extract value.",
    explanationPt: "JJ é overpair forte em 8-6-3. Em potes multiway, usamos bet size menor (~50% pot) para manter mais mãos no pote e extrair valor.",
    context: JSON.stringify({
      spr: 12.7,
      boardTexture: "dry",
      handStrength: "strong",
      multiway: true
    }),
    difficulty: "intermediate",
    category: "multiway",
    tags: JSON.stringify(["multiway", "overpair", "value_bet", "sizing"])
  },

  // === CHECK-RAISE SPOTS ===
  {
    name: "Turn Check-Raise with Combo Draw",
    nameEn: "Turn Check-Raise with Combo Draw",
    namePt: "Check-Raise no Turn com Combo Draw",
    description: "Check-raise opportunity on turn with 9♥8♥ on 7♥6♣2♦5♠",
    descriptionEn: "Check-raise opportunity on turn with 9♥8♥ on 7♥6♣2♦5♠",
    descriptionPt: "Oportunidade de check-raise no turn com 9♥8♥ em 7♥6♣2♦5♠",
    gameType: "cash",
    street: "turn",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "hearts", rank: "9", value: 9 },
      { suit: "hearts", rank: "8", value: 8 }
    ]),
    communityCards: JSON.stringify([
      { suit: "hearts", rank: "7", value: 7 },
      { suit: "clubs", rank: "6", value: 6 },
      { suit: "diamonds", rank: "2", value: 2 },
      { suit: "spades", rank: "5", value: 5 }
    ]),
    potSize: 45.0,
    playerStack: 170.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 30, isActive: true, stackSize: 164 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.10,
        explanation: "Fold desperdiça mão com muita equity",
        explanationEn: "Fold wastes hand with lots of equity",
        explanationPt: "Fold desperdiça mão com muita equity"
      },
      {
        action: "call",
        amount: 30,
        isCorrect: false,
        frequency: 0.50,
        explanation: "Call é passivo demais com tanto equity",
        explanationEn: "Call is too passive with so much equity",
        explanationPt: "Call é passivo demais com tanto equity"
      },
      {
        action: "raise",
        amount: 90,
        sizing: "large",
        isCorrect: true,
        frequency: 0.40,
        explanation: "Check-raise com combo draw - 13 outs para nuts",
        explanationEn: "Check-raise with combo draw - 13 outs to nuts",
        explanationPt: "Check-raise com combo draw - 13 outs para nuts"
      }
    ]),
    correctAction: "raise",
    equity: 0.52,
    frequency: 0.40,
    explanation: "98h em 7-6-2-5 tem straight draw + flush draw + overcards = 13 outs. Check-raise é correto com tanta equity.",
    explanationEn: "98h on 7-6-2-5 has straight draw + flush draw + overcards = 13 outs. Check-raise is correct with so much equity.",
    explanationPt: "98h em 7-6-2-5 tem straight draw + flush draw + overcards = 13 outs. Check-raise é correto com tanta equity.",
    context: JSON.stringify({
      spr: 3.6,
      boardTexture: "coordinated",
      handStrength: "draw",
      outs: 13,
      drawType: "combo_draw"
    }),
    difficulty: "advanced",
    category: "check_raise",
    tags: JSON.stringify(["turn_play", "combo_draw", "semi_bluff", "check_raise"])
  },

  // === SQUEEZE SCENARIOS ===
  {
    name: "SB Squeeze with A9s",
    nameEn: "SB Squeeze with A9s",
    namePt: "Squeeze do SB com A9s",
    description: "UTG opened, MP and CO called. SB squeeze spot with A9s",
    descriptionEn: "UTG opened, MP and CO called. SB squeeze spot with A9s",
    descriptionPt: "UTG abriu, MP e CO pagaram. Spot de squeeze do SB com A9s",
    gameType: "cash",
    street: "preflop",
    position: "SB",
    stackDepth: "deep",
    playerCards: JSON.stringify([
      { suit: "spades", rank: "A", value: 14 },
      { suit: "spades", rank: "9", value: 9 }
    ]),
    communityCards: JSON.stringify([]),
    potSize: 10.0,
    playerStack: 99.5,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "raise", amount: 2.5, isActive: true, stackSize: 97.5 },
      { position: "MP", action: "call", amount: 2.5, isActive: true, stackSize: 97.5 },
      { position: "CO", action: "call", amount: 2.5, isActive: true, stackSize: 97.5 },
      { position: "BTN", action: "fold", isActive: false, stackSize: 100 },
      { position: "BB", action: "pending", isActive: true, stackSize: 99 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.40,
        explanation: "Fold desperdiça boa oportunidade de squeeze",
        explanationEn: "Fold wastes good squeeze opportunity",
        explanationPt: "Fold desperdiça boa oportunidade de squeeze"
      },
      {
        action: "call",
        amount: 2.0,
        isCorrect: false,
        frequency: 0.10,
        explanation: "Call cria pot multiway fora de posição",
        explanationEn: "Call creates multiway pot out of position",
        explanationPt: "Call cria pot multiway fora de posição"
      },
      {
        action: "raise",
        amount: 14.0,
        sizing: "squeeze",
        isCorrect: true,
        frequency: 0.50,
        explanation: "Squeeze com A9s - boa mão com fold equity",
        explanationEn: "Squeeze with A9s - good hand with fold equity",
        explanationPt: "Squeeze com A9s - boa mão com fold equity"
      }
    ]),
    correctAction: "raise",
    equity: 0.32,
    frequency: 0.50,
    explanation: "A9s é ideal para squeeze. Com múltiplos callers, temos fold equity significativa e mão jogável se chamada.",
    explanationEn: "A9s is ideal for squeeze. With multiple callers, we have significant fold equity and playable hand if called.",
    explanationPt: "A9s é ideal para squeeze. Com múltiplos callers, temos fold equity significativa e mão jogável se chamada.",
    context: JSON.stringify({
      spr: 7,
      boardTexture: "preflop",
      handStrength: "speculative",
      squeeze_spot: true
    }),
    difficulty: "advanced",
    category: "squeeze",
    tags: JSON.stringify(["preflop", "squeeze", "multiway", "fold_equity"])
  }
]

async function populateRealisticScenarios() {
  console.log('🎯 Iniciando população de cenários GTO 100% realistas...')
  
  try {
    // Limpar cenários existentes
    await prisma.gTOScenario.deleteMany({})
    console.log('🗑️ Cenários antigos removidos')
    
    // Inserir novos cenários realistas
    for (const scenario of realisticGTOScenarios) {
      await prisma.gTOScenario.create({
        data: {
          name: scenario.name,
          nameEn: scenario.nameEn,
          namePt: scenario.namePt,
          description: scenario.description,
          descriptionEn: scenario.descriptionEn,
          descriptionPt: scenario.descriptionPt,
          gameType: scenario.gameType,
          street: scenario.street,
          position: scenario.position,
          stackDepth: scenario.stackDepth,
          playerCards: scenario.playerCards,
          communityCards: scenario.communityCards,
          potSize: scenario.potSize,
          playerStack: scenario.playerStack,
          opponentActions: scenario.opponentActions,
          availableActions: scenario.availableActions,
          correctAction: scenario.correctAction,
          equity: scenario.equity,
          frequency: scenario.frequency,
          explanation: scenario.explanation,
          explanationEn: scenario.explanationEn,
          explanationPt: scenario.explanationPt,
          context: scenario.context,
          difficulty: scenario.difficulty,
          category: scenario.category,
          tags: scenario.tags
        }
      })
      console.log(`✅ Cenário criado: ${scenario.name}`)
    }
    
    console.log(`🎉 ${realisticGTOScenarios.length} cenários GTO realistas criados com sucesso!`)
    
    // Verificar contagem final
    const count = await prisma.gTOScenario.count()
    console.log(`📊 Total de cenários no banco: ${count}`)
    
    // Mostrar estatísticas por categoria
    const categories = await prisma.gTOScenario.groupBy({
      by: ['category'],
      _count: { category: true }
    })
    
    console.log('\n📈 Cenários por categoria:')
    categories.forEach(cat => {
      console.log(`  ${cat.category}: ${cat._count.category} cenários`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao popular cenários:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  populateRealisticScenarios()
}

module.exports = { populateRealisticScenarios, realisticGTOScenarios }
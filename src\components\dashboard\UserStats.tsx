'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Award, 
  Clock, 
  CheckCircle2, 
  XCircle
} from 'lucide-react'
import { UserStatistics } from '@/types'
import { useLanguage } from '@/hooks/useLanguage'

interface UserStatsProps {
  userId: string
  className?: string
}

export default function UserStats({ userId, className }: UserStatsProps) {
  const { t } = useLanguage()
  const [stats, setStats] = useState<UserStatistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/user/stats/${userId}`)

        if (!response.ok) {
          throw new Error(t('statistics.errorFetchingStats'))
        }

        const data = await response.json()
        setStats(data)
      } catch (err) {
        setError((err as Error).message)
      } finally {
        setIsLoading(false)
      }
    }

    if (userId) {
      fetchStats()
    }
  }, [userId, t])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 text-center">
        <p className="text-red-300">{t('statistics.errorLoadingStats')}: {error || t('statistics.dataNotAvailable')}</p>
      </div>
    )
  }

  // Calcular estatísticas derivadas
  const overallAccuracy = stats.totalHands > 0 
    ? (stats.correctDecisions / stats.totalHands * 100).toFixed(1) 
    : '0.0'

  const streetAccuracy = [
    { name: t('streets.preflop'), value: stats.preflopAccuracy * 100 },
    { name: t('streets.flop'), value: stats.flopAccuracy * 100 },
    { name: t('streets.turn'), value: stats.turnAccuracy * 100 },
    { name: t('streets.river'), value: stats.riverAccuracy * 100 }
  ]

  const actionFrequency = [
    { name: t('actions.fold'), value: stats.foldFrequency * 100 },
    { name: t('actions.call'), value: stats.callFrequency * 100 },
    { name: t('actions.raise'), value: stats.raiseFrequency * 100 },
    { name: 'C-Bet', value: stats.cBetFrequency * 100 },
    { name: 'Check-Raise', value: stats.checkRaiseFrequency * 100 }
  ]

  return (
    <div className={className}>
      {/* Visão geral */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-xl p-5 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400 text-sm">{t('statistics.handsPlayed')}</h3>
            <BarChart3 className="w-5 h-5 text-blue-400" />
          </div>
          <div className="text-2xl font-bold text-white">{stats.totalHands}</div>
          <div className="text-xs text-gray-400 mt-1">
            {stats.totalHands > 0 ? `${stats.correctDecisions} ${t('statistics.correctDecisions')}` : t('statistics.noHandsPlayed')}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 rounded-xl p-5 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400 text-sm">{t('statistics.overallAccuracy')}</h3>
            <PieChart className="w-5 h-5 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">{overallAccuracy}%</div>
          <div className="text-xs text-gray-400 mt-1">
            {stats.totalHands > 0 
              ? `${stats.correctDecisions} ${t('statistics.decisionsOf')} ${stats.totalHands} ${t('statistics.decisions')}` 
              : t('statistics.noDecisionsYet')}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-xl p-5 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400 text-sm">{t('statistics.currentStreak')}</h3>
            <TrendingUp className="w-5 h-5 text-yellow-400" />
          </div>
          <div className="text-2xl font-bold text-white">{stats.currentStreak}</div>
          <div className="text-xs text-gray-400 mt-1">
            {t('statistics.record')}: {stats.longestStreak} {t('statistics.hits')}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-xl p-5 border border-gray-700"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400 text-sm">{t('statistics.averageEquity')}</h3>
            <Award className="w-5 h-5 text-purple-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {(stats.averageEquity * 100).toFixed(1)}%
          </div>
          <div className="text-xs text-gray-400 mt-1">
            {t('statistics.inPlayedHands')}
          </div>
        </motion.div>
      </div>

      {/* Precisão por street */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-gray-800 rounded-xl p-6 border border-gray-700 mb-8"
      >
        <div className="flex items-center mb-6">
          <Clock className="w-5 h-5 text-blue-400 mr-2" />
          <h3 className="text-lg font-semibold text-white">{t('statistics.accuracyByStreet')}</h3>
        </div>

        <div className="space-y-4">
          {streetAccuracy.map((street) => (
            <div key={street.name} className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">{street.name}</span>
                <span className="text-gray-300">{street.value.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full" 
                  style={{ width: `${street.value}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Frequência de ações */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-gray-800 rounded-xl p-6 border border-gray-700"
      >
        <div className="flex items-center mb-6">
          <PieChart className="w-5 h-5 text-green-400 mr-2" />
          <h3 className="text-lg font-semibold text-white">{t('statistics.actionFrequency')}</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {actionFrequency.map((action) => (
            <div key={action.name} className="bg-gray-700/50 rounded-lg p-4 text-center">
              <div className="text-lg font-bold text-white mb-1">{action.value.toFixed(1)}%</div>
              <div className="text-sm text-gray-400">{action.name}</div>
            </div>
          ))}
        </div>
      </motion.div>


    </div>
  )
}
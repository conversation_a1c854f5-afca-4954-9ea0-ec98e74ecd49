'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  User as UserIcon, 
  Mail, 
  Calendar, 
  Trophy, 
  Target,
  BarChart3,
  Settings,
  Edit3,
  Save,
  X,
  Shield,
  Clock
} from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'
import { User } from '@/types'
// import Navbar from '@/components/dashboard/Navbar' // Removido - componente não existe
import { getLevelFromXP, getXPForNextLevel, getLevelProgress } from '@/lib/utils'

export default function ProfilePage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    username: ''
  })
  const [userStats, setUserStats] = useState({
    totalHands: 0,
    correctDecisions: 0,
    longestStreak: 0,
    averageTime: 0,
    favoriteCategory: '',
    joinDate: ''
  })
  const [achievements, setAchievements] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    loadDefaultUser()
  }, [])

  const loadDefaultUser = async () => {
    try {
      setIsLoading(true)
      const defaultUserId = 'cmdhptt1n0000m0rwt9dn15fu'
      
      // Carregar dados do usuário
      const userResponse = await fetch(`/api/user/profile/${defaultUserId}`)
      if (userResponse.ok) {
        const userData = await userResponse.json()
        setUser(userData)
        setEditForm({
          username: userData.username
        })
        
        // Carregar estatísticas
        const statsResponse = await fetch(`/api/user/stats/${defaultUserId}`)
        if (statsResponse.ok) {
          const stats = await statsResponse.json()
          setUserStats({
            totalHands: stats.totalHands || 0,
            correctDecisions: stats.correctDecisions || 0,
            longestStreak: stats.longestStreak || 0,
            averageTime: stats.averageTime || 0,
            favoriteCategory: stats.favoriteCategory || 'Preflop',
            joinDate: userData.createdAt || new Date().toISOString()
          })
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveProfile = async () => {
    if (!user) return
    
    try {
      setIsSaving(true)
      
      const response = await fetch(`/api/user/profile/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username: editForm.username })
      })
      
      if (response.ok) {
        const updatedUser = await response.json()
        setUser(updatedUser)
        setIsEditing(false)
      } else {
        throw new Error('Erro ao atualizar perfil')
      }
    } catch (error) {
      console.error('Erro ao salvar perfil:', error)
      alert('Erro ao salvar perfil. Tente novamente.')
    } finally {
      setIsSaving(false)
    }
  }

  const cancelEdit = () => {
    if (user) {
      setEditForm({
        username: user.username
      })
    }
    setIsEditing(false)
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const currentLevel = getLevelFromXP(user.totalPoints)
  const nextLevelXP = getXPForNextLevel(currentLevel)
  const progress = getLevelProgress(user.totalPoints)
  const accuracy = userStats.totalHands > 0 ? (userStats.correctDecisions / userStats.totalHands * 100) : 0

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Perfil Principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* Informações Básicas */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">{t('profile.profileInfo')}</h2>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>{t('profile.edit')}</span>
                  </button>
                ) : (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleSaveProfile}
                      disabled={isSaving}
                      className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <Save className="w-4 h-4" />
                      <span>{isSaving ? t('profile.saving') : t('profile.save')}</span>
                    </button>
                    <button
                      onClick={cancelEdit}
                      className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4" />
                      <span>{t('profile.cancel')}</span>
                    </button>
                  </div>
                )}
              </div>

              <div className="flex items-start space-x-6">
                {/* Avatar */}
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <UserIcon className="w-12 h-12 text-white" />
                </div>

                {/* Informações */}
                <div className="flex-1 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      {t('profile.username')}
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editForm.username}
                        onChange={(e) => setEditForm(prev => ({ ...prev, username: e.target.value }))}
                        className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    ) : (
                      <p className="text-white text-lg font-medium">{user.username}</p>
                    )}
                  </div>



                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{t('profile.memberSince')} {new Date(userStats.joinDate).toLocaleDateString('pt-BR')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Shield className="w-4 h-4" />
                      <span>{t('profile.level')} {currentLevel}</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Estatísticas Detalhadas */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-8"
            >
              <h3 className="text-xl font-bold text-white mb-6">{t('profile.detailedStats')}</h3>
              
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <Target className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-white">{userStats.totalHands}</div>
                    <div className="text-sm text-gray-400">{t('profile.handsPlayed')}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <BarChart3 className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-white">{accuracy.toFixed(1)}%</div>
                    <div className="text-sm text-gray-400">{t('profile.accuracy')}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <Trophy className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-white">{userStats.longestStreak}</div>
                    <div className="text-sm text-gray-400">{t('profile.bestStreak')}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-white">{userStats.averageTime}s</div>
                    <div className="text-sm text-gray-400">{t('profile.averageTime')}</div>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Conquistas Recentes */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-8"
            >
              <h3 className="text-xl font-bold text-white mb-6">{t('profile.recentAchievements')}</h3>
              
              {achievements.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {achievements.slice(0, 4).map((achievement: any, index) => (
                    <div
                      key={achievement.id}
                      className="flex items-center space-x-3 bg-gray-700 rounded-lg p-4"
                    >
                      <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <Trophy className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-medium">{achievement.achievement.title}</div>
                        <div className="text-gray-400 text-sm">{achievement.achievement.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">{t('profile.noAchievementsYet')}</p>
                  <p className="text-gray-500 text-sm">{t('profile.keepTrainingUnlock')}</p>
                </div>
              )}
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Progresso de Nível */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">{t('profile.levelProgress')}</h3>
              
              <div className="text-center mb-4">
                <div className="text-3xl font-bold text-white mb-1">{t('profile.level')} {currentLevel}</div>
                <div className="text-gray-400 text-sm">{user.totalPoints} / {nextLevelXP} XP</div>
              </div>
              
              <div className="w-full bg-gray-700 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              
              <div className="text-center text-sm text-gray-400">
                {nextLevelXP - user.totalPoints} {t('profile.xpToNextLevel')}
              </div>
            </motion.div>

            {/* Categoria Favorita */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">{t('profile.favoriteCategory')}</h3>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <div className="text-white font-medium">{userStats.favoriteCategory}</div>
                <div className="text-gray-400 text-sm">{t('profile.mostPracticed')}</div>
              </div>
            </motion.div>

            {/* Links Rápidos */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6"
            >
              <h3 className="text-lg font-bold text-white mb-4">{t('profile.quickLinks')}</h3>
              
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/statistics')}
                  className="w-full flex items-center space-x-3 text-left text-gray-300 hover:text-white hover:bg-gray-700 p-3 rounded-lg transition-colors"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>{t('profile.viewStats')}</span>
                </button>
                
                <button
                  onClick={() => router.push('/achievements')}
                  className="w-full flex items-center space-x-3 text-left text-gray-300 hover:text-white hover:bg-gray-700 p-3 rounded-lg transition-colors"
                >
                  <Trophy className="w-5 h-5" />
                  <span>{t('profile.myAchievements')}</span>
                </button>
                
                <button
                  onClick={() => router.push('/leaderboard')}
                  className="w-full flex items-center space-x-3 text-left text-gray-300 hover:text-white hover:bg-gray-700 p-3 rounded-lg transition-colors"
                >
                  <Trophy className="w-5 h-5" />
                  <span>{t('profile.globalRanking')}</span>
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
# 📋 Como Criar Cenários GTO Realistas e Coerentes

## 🎯 Princípios Fundamentais

### 1. **REALISMO ABSOLUTO**
- Todos os dados devem refletir situações reais de poker
- Cartas, posições, ações e valores devem ser coerentes
- Nunca invente situações impossíveis ou irreais

### 2. **COERÊNCIA ESTRATÉGICA**
- As explicações devem ser fundamentadas em teoria GTO
- Equity e frequências devem ser realistas
- Ações recomendadas devem fazer sentido estratégico

### 3. **PRECISÃO TÉCNICA**
- Pot odds, stack sizes e bet sizes proporcionais
- Cartas não podem se repetir no mesmo cenário
- Streets devem corresponder às cartas comunitárias

---

## 📝 Estrutura do JSON

### Campos Obrigatórios

```json
{
  "name": "Nome descritivo do cenário",
  "description": "Descrição clara da situação",
  "gameType": "cash" | "tournament",
  "street": "preflop" | "flop" | "turn" | "river",
  "position": "UTG" | "MP" | "CO" | "BTN" | "SB" | "BB",
  "playerCards": {
    "card1": "As",
    "card2": "Kh"
  },
  "correctAction": "fold" | "call" | "raise" | "check" | "bet",
  "explanation": "Explicação detalhada da jogada GTO",
  "availableActions": [
    {
      "action": "fold",
      "frequency": 0.3,
      "isCorrect": false,
      "explanation": "Por que esta ação é incorreta"
    }
  ]
}
```

### Campos Opcionais

```json
{
  "customFilter": "Nome do filtro personalizado",
  "customFilterDescription": "Descrição do filtro",
  "customFilterColor": "#3B82F6",
  "difficulty": "beginner" | "intermediate" | "advanced",
  "category": "Categoria do cenário",
  "tags": ["tag1", "tag2"]
}
```

---

## 🏷️ Filtros Personalizados

### O que são?
Filtros personalizados permitem agrupar cenários por temas específicos para criar treinamentos focados.

### Exemplos de Filtros
- **"Cbet Flop"** - Todos os cenários de continuation bet no flop
- **"3bet Preflop"** - Situações de 3-bet no preflop
- **"River Bluff Catch"** - Decisões de call vs bluff no river
- **"Turn Aggression"** - Jogadas agressivas no turn
- **"Squeeze Play"** - Situações de squeeze

### Como Usar
```json
{
  "customFilter": "Cbet Flop",
  "customFilterDescription": "Cenários de continuation bet no flop",
  "customFilterColor": "#10B981"
}
```

### Comportamento
- **Upload JSON**: Filtro é criado automaticamente se não existir
- **Criação Manual**: Sistema pergunta se deseja criar o filtro
- **Cores**: Use códigos hexadecimais para identificação visual

---

## 🃏 Formato das Cartas

### Ranks (Valores)
- `A` = Ás
- `K` = Rei  
- `Q` = Dama
- `J` = Valete
- `T` = 10
- `9, 8, 7, 6, 5, 4, 3, 2` = Números

### Suits (Naipes)
- `s` = Espadas (♠)
- `h` = Copas (♥)
- `d` = Ouros (♦)
- `c` = Paus (♣)

### Exemplos Corretos
- `"As"` = Ás de Espadas
- `"Kh"` = Rei de Copas
- `"Qd"` = Dama de Ouros
- `"Jc"` = Valete de Paus
- `"Ts"` = 10 de Espadas

---

## 🎲 Regras de Coerência

### 1. **Cartas Únicas**
```json
// ❌ ERRADO - Cartas repetidas
"playerCards": {"card1": "As", "card2": "As"}

// ✅ CORRETO - Cartas diferentes
"playerCards": {"card1": "As", "card2": "Kh"}
```

### 2. **Streets e Community Cards**
```json
// ❌ ERRADO - Preflop com cartas comunitárias
"street": "preflop",
"communityCards": {"flop": ["As", "Kh", "Qd"]}

// ✅ CORRETO - Preflop sem cartas comunitárias
"street": "preflop",
"communityCards": {"flop": null, "turn": null, "river": null}

// ✅ CORRETO - Flop com 3 cartas
"street": "flop",
"communityCards": {"flop": ["As", "Kh", "Qd"], "turn": null, "river": null}
```

### 3. **Pot Sizes Realistas**
```json
// ❌ ERRADO - Pot muito pequeno para a situação
"street": "river",
"potSize": 2,
"playerStack": 200

// ✅ CORRETO - Pot proporcional
"street": "river", 
"potSize": 120,
"playerStack": 85
```

---

## 💰 Sizing Guidelines

### Preflop
- **Open raise**: 2.5-3bb
- **3-bet**: 3x do raise original
- **4-bet**: 2.2-2.5x do 3-bet

### Postflop
- **C-bet**: 60-75% do pot
- **Turn bet**: 65-80% do pot  
- **River bet**: 70-100% do pot

### Stack Depths
- **Shallow**: 20-40bb
- **Medium**: 40-80bb
- **Deep**: 80bb+

---

## 🎯 Exemplos de Situações Realistas

### 1. **Preflop 3-bet Spot**
```json
{
  "name": "UTG vs BTN 3-bet com AQo",
  "description": "Você abriu UTG com AQo e o BTN fez 3-bet. Como proceder?",
  "gameType": "cash",
  "street": "preflop",
  "position": "UTG",
  "playerCards": {"card1": "Ah", "card2": "Qc"},
  "potSize": 45,
  "playerStack": 170,
  "opponentActions": [
    {"position": "BTN", "action": "raise", "amount": 30, "stackSize": 170}
  ],
  "availableActions": [
    {
      "action": "fold",
      "frequency": 0.8,
      "isCorrect": true,
      "explanation": "AQo vs 3-bet de BTN é fold padrão"
    }
  ],
  "correctAction": "fold",
  "explanation": "AQo vs 3-bet de BTN é fold padrão devido à equity insuficiente",
  "customFilter": "3bet Defense",
  "customFilterDescription": "Cenários de defesa contra 3-bet",
  "customFilterColor": "#EF4444",
  "difficulty": "intermediate",
  "category": "preflop"
}
```

### 2. **Flop C-bet Decision**
```json
{
  "name": "C-bet com Overpair",
  "description": "Você tem JJ no flop 852 rainbow. BB checou. Fazer c-bet?",
  "gameType": "cash",
  "street": "flop", 
  "position": "CO",
  "playerCards": {"card1": "Jh", "card2": "Js"},
  "communityCards": {"flop": ["8c", "5d", "2h"], "turn": null, "river": null},
  "potSize": 20,
  "playerStack": 190,
  "opponentActions": [
    {"position": "BB", "action": "check", "stackSize": 190}
  ],
  "availableActions": [
    {
      "action": "bet",
      "amount": 15,
      "frequency": 0.9,
      "isCorrect": true,
      "explanation": "Overpair deve apostar para value"
    }
  ],
  "correctAction": "bet",
  "explanation": "Overpair em board seco deve apostar para value e proteção",
  "customFilter": "Cbet Flop",
  "customFilterDescription": "Cenários de continuation bet no flop",
  "customFilterColor": "#10B981",
  "difficulty": "beginner",
  "category": "cbet"
}
```

---

## ⚠️ Erros Comuns a Evitar

### 1. **Situações Impossíveis**
- Foldar nuts no river
- 3-bet com 72o de UTG
- Check-raise com air em board paired

### 2. **Inconsistências Técnicas**
- Pot odds que não batem
- Stacks que não fazem sentido
- Frequências que somam mais que 1.0

### 3. **Explicações Vagas**
- "É uma boa mão" ❌
- "Sempre faça isso" ❌  
- "Depende do oponente" ❌

### 4. **Explicações Corretas**
- "AK tem 31% de equity vs range de call do BB" ✅
- "Board seco favorece range do agressor" ✅
- "Pot odds de 2.5:1 justificam call com 29% de equity" ✅

---

## 📊 Equity e Frequências Realistas

### Ranges Típicos por Posição (Preflop)
- **UTG**: ~12% (AA-77, AK-AJ, KQ)
- **MP**: ~15% (adiciona 66-22, AT, KJ)
- **CO**: ~25% (adiciona suited connectors, Ax)
- **BTN**: ~45% (muito wide)
- **SB**: ~35% (vs BTN steal)
- **BB**: ~20% (vs UTG open)

### Equity Aproximada
- **AA vs random**: 85%
- **AK vs random**: 65%
- **22 vs AK**: 50%
- **AK vs QQ**: 30%
- **Flush draw**: 35%
- **Straight draw**: 32%

---

## ✅ Checklist de Qualidade

Antes de submeter um cenário, verifique:

- [ ] Nome descreve claramente a situação
- [ ] Cartas do jogador correspondem ao nome
- [ ] Board cards fazem sentido para a street
- [ ] Pot size é proporcional à ação
- [ ] Stack sizes são realistas
- [ ] Ações dos oponentes são lógicas
- [ ] Explicação é fundamentada em teoria
- [ ] Equity está na faixa correta
- [ ] Frequências somam ≤ 1.0
- [ ] Situação é comum no poker real
- [ ] Filtro personalizado é relevante (se usado)
- [ ] Descrição do filtro é clara (se criando novo)
- [ ] Cor do filtro é adequada para identificação

---

## 🎓 Recursos Recomendados

### Ferramentas GTO
- **PioSolver**: Para verificar soluções exatas
- **GTO Wizard**: Para ranges e frequências
- **Flopzilla**: Para análise de equity

### Literatura
- "Modern Poker Theory" - Michael Acevedo
- "Applications of No-Limit Hold'em" - Matthew Janda
- "Play Optimal Poker" - Andrew Brokos

---

## 📞 Suporte

Se tiver dúvidas sobre como criar cenários realistas:

1. Consulte os exemplos no template JSON
2. Verifique se a situação realmente acontece no poker
3. Confirme que a explicação faz sentido estratégico
4. Teste com ferramentas GTO quando possível

**Lembre-se**: Qualidade > Quantidade. É melhor ter 5 cenários perfeitos do que 50 cenários ruins.
import { NextResponse } from 'next/server'
import { getRandomScenarios } from '@/lib/scenario-service'

export async function GET() {
  try {
    const scenarios = await getRandomScenarios(5)
    return NextResponse.json({ success: true, scenarios })
  } catch (error) {
    console.error('Erro ao buscar cenários demo:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao carregar cenários' },
      { status: 500 }
    )
  }
}
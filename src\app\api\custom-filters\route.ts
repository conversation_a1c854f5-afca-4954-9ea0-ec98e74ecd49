import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET - Listar todos os filtros personalizados
export async function GET(request: NextRequest) {
  try {
    const filters = await prisma.customFilter.findMany({
      include: {
        _count: {
          select: { scenarios: true }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json({
      success: true,
      filters
    })

  } catch (error) {
    console.error('Erro ao buscar filtros:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// POST - Criar novo filtro personalizado
export async function POST(request: NextRequest) {
  try {
    const { name, description, color } = await request.json()

    if (!name) {
      return NextResponse.json(
        { error: 'Nome do filtro é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se já existe
    const existingFilter = await prisma.customFilter.findUnique({
      where: { name }
    })

    if (existingFilter) {
      return NextResponse.json(
        { error: 'Filtro com este nome já existe' },
        { status: 400 }
      )
    }

    const filter = await prisma.customFilter.create({
      data: {
        name,
        description: description || null,
        color: color || '#3B82F6' // Azul padrão
      }
    })

    return NextResponse.json({
      success: true,
      filter,
      message: 'Filtro personalizado criado com sucesso!'
    })

  } catch (error) {
    console.error('Erro ao criar filtro:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
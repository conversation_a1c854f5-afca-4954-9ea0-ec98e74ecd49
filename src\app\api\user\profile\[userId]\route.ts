import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Obter dados do corpo da requisição
    const { username } = await request.json()

    // Validar dados
    if (!username) {
      return NextResponse.json(
        { error: 'Nome de usuário é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o nome de usuário já existe (exceto para o usuário atual)
    const existingUser = await prisma.user.findFirst({
      where: {
        username,
        NOT: {
          id: params.userId
        }
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Nome de usuário já está em uso' },
        { status: 400 }
      )
    }

    // Atualizar usuário
    const updatedUser = await prisma.user.update({
      where: { id: params.userId },
      data: {
        username,
        updatedAt: new Date()
      }
    })

    return NextResponse.json(updatedUser)

  } catch (error) {
    console.error('Erro ao atualizar perfil:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Buscar usuário
    const user = await prisma.user.findUnique({
      where: { id: params.userId },
      include: {
        statistics: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      )
    }

    return NextResponse.json(user)

  } catch (error) {
    console.error('Erro ao buscar perfil:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
async function testSpecificScenario() {
  try {
    const response = await fetch('http://localhost:3000/api/scenarios/demo');
    const data = await response.json();
    
    if (data.success && data.scenarios) {
      // Procurar um cenário de river
      const riverScenario = data.scenarios.find(s => s.gameState.street === 'river');
      
      if (riverScenario) {
        console.log('=== CENÁRIO DE RIVER ENCONTRADO ===');
        console.log('Nome:', riverScenario.name);
        console.log('Street:', riverScenario.gameState.street);
        console.log('Community Cards:', riverScenario.gameState.communityCards.length);
        console.log('Player Cards:', riverScenario.gameState.playerCards.length);
        
        console.log('\n=== CARTAS COMUNITÁRIAS ===');
        riverScenario.gameState.communityCards.forEach((card, index) => {
          console.log(`${index + 1}. ${card.rank}${card.suit}`);
        });
        
        console.log('\n=== CARTAS DO JOGADOR ===');
        riverScenario.gameState.playerCards.forEach((card, index) => {
          console.log(`${index + 1}. ${card.rank}${card.suit}`);
        });
      } else {
        console.log('Nenhum cenário de river encontrado');
      }
    }
  } catch (error) {
    console.error('Erro:', error);
  }
}

testSpecificScenario();
# 🎯 FLUXO COMPLETO DE CENÁRIOS GTO - FRONTEND E MODULARIDADE

## 📊 **ARQUITETURA ATUAL**

### 🗄️ **1. BANCO DE DADOS (SQLite)**
```sql
-- Tabela: gto_scenarios
CREATE TABLE gto_scenarios (
  id              TEXT PRIMARY KEY,
  name            TEXT,
  nameEn          TEXT,
  namePt          TEXT,
  description     TEXT,
  gameType        TEXT,     -- "cash" | "tournament"
  street          TEXT,     -- "preflop" | "flop" | "turn" | "river"
  position        TEXT,     -- "UTG" | "MP" | "CO" | "BTN" | "SB" | "BB"
  
  -- <PERSON><PERSON> da situação (JSON)
  playerCards     TEXT,     -- {"card1": "Kh", "card2": "Qd"}
  communityCards  TEXT,     -- {"flop": ["As", "7c", "2h"]}
  opponentActions TEXT,     -- [{"position": "BB", "action": "call"}]
  
  -- A<PERSON><PERSON><PERSON> disponí<PERSON> (JSON)
  availableActions TEXT,    -- [{"action": "bet", "isCorrect": true, "frequency": 0.75}]
  correctAction   TEXT,     -- "bet"
  
  -- M<PERSON><PERSON>as GTO
  equity          REAL,     -- 0.35
  frequency       REAL,     -- 0.75
  explanation     TEXT,
  
  -- Classificação
  difficulty      TEXT,     -- "beginner" | "intermediate" | "advanced"
  category        TEXT,     -- "cbet" | "3bet_defense" | "bluff_catch"
  tags            TEXT      -- ["continuation_bet", "dry_board"]
)
```

### 🔄 **2. FLUXO DE DADOS - FRONTEND**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   BANCO SQLite  │───▶│  SCENARIO-SERVICE │───▶│   FRONTEND      │
│                 │    │                  │    │                 │
│ • Raw JSON data │    │ • processScenario│    │ • PokerTable    │
│ • GTO scenarios │    │ • convertCard    │    │ • Quiz/Training │
│ • Metadata      │    │ • buildGameState │    │ • User Interface│
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 🎮 **3. COMPONENTES PRINCIPAIS**

#### **A) ScenarioService (`src/lib/scenario-service.ts`)**
```typescript
// FUNÇÃO PRINCIPAL: Converte dados do banco para interface
function processScenario(dbScenario: GTOScenarioFromDB): ProcessedScenario {
  // 1. Parse JSON fields
  const playerCards = JSON.parse(dbScenario.playerCards)
  const communityCards = JSON.parse(dbScenario.communityCards)
  const availableActions = JSON.parse(dbScenario.availableActions)
  
  // 2. Convert card strings to Card objects
  const convertCard = (cardStr: string) => ({
    suit: suitMap[cardStr[1]],
    rank: cardStr[0],
    value: valueMap[cardStr[0]]
  })
  
  // 3. Build GameState
  const gameState: GameState = {
    street: dbScenario.street,
    pot: dbScenario.potSize,
    communityCards: communityCards.flop?.map(convertCard) || [],
    playerCards: [convertCard(playerCards.card1), convertCard(playerCards.card2)],
    position: { name: dbScenario.position, ... },
    // ...
  }
  
  // 4. Return processed scenario
  return { id, name, description, gameState, recommendation, availableActions }
}
```

#### **B) API Route (`/api/scenarios/demo`)**
```typescript
export async function GET() {
  const scenarios = await getRandomScenarios(5)  // Busca do banco
  return NextResponse.json({ success: true, scenarios })
}
```

#### **C) Quiz Component (`/dashboard/training/quiz`)**
```typescript
const generateQuizQuestions = async () => {
  // 1. Fetch scenarios from API
  const response = await fetch('/api/scenarios/demo')
  const data = await response.json()
  
  // 2. Convert to quiz format
  const quizQuestions = data.scenarios.map(scenario => ({
    id: scenario.id,
    scenario: { ...scenario.gameState },
    options: scenario.availableActions.map(action => ({
      type: action.action,
      amount: action.amount
    })),
    correctAnswer: scenario.recommendation.action
  }))
  
  setQuestions(quizQuestions)
}
```

#### **D) PokerTable Component**
```typescript
// Recebe gameState processado e renderiza a interface
<PokerTable
  gameState={scenario.gameState}
  availableActions={scenario.availableActions}
  onAction={handleAction}
  recommendation={scenario.recommendation}
/>
```

---

## 🔧 **MODULARIDADE PARA NOVOS CENÁRIOS**

### ✅ **PONTOS FORTES ATUAIS**

1. **Separação Clara de Responsabilidades**
   - ✅ Banco de dados: Armazena dados brutos
   - ✅ ScenarioService: Processa e converte dados
   - ✅ Components: Renderizam interface

2. **Formato JSON Flexível**
   - ✅ `availableActions`: Suporta qualquer combinação de ações
   - ✅ `playerCards`/`communityCards`: Flexível para qualquer situação
   - ✅ `context`: Dados adicionais extensíveis

3. **API Padronizada**
   - ✅ Endpoint consistente (`/api/scenarios/demo`)
   - ✅ Formato de resposta padronizado
   - ✅ Error handling implementado

### 🚀 **COMO ADICIONAR NOVOS CENÁRIOS**

#### **Método 1: Script de População (Recomendado)**
```typescript
// 1. Adicionar ao array em scripts/populate-real-scenarios.ts
const newScenario = {
  id: "scenario_004_river_decision",
  namePt: "Decisão no River com Flush Draw",
  street: "river",
  position: "CO",
  playerCards: JSON.stringify({ card1: "9h", card2: "8h" }),
  communityCards: JSON.stringify({
    flop: ["7h", "6c", "2d"],
    turn: "5s",
    river: "Ac"
  }),
  availableActions: JSON.stringify([
    {
      action: "fold",
      isCorrect: false,
      frequency: 0.30,
      explanation: "Fold desperdiça equity com straight."
    },
    {
      action: "call",
      isCorrect: true,
      frequency: 0.70,
      explanation: "Call correto com straight nuts."
    }
  ]),
  correctAction: "call",
  // ... outros campos
}

// 2. Executar script
npm run populate-scenarios
```

#### **Método 2: API Administrativa (Futuro)**
```typescript
// POST /api/admin/scenarios
{
  "name": "Novo Cenário",
  "street": "turn",
  "position": "BTN",
  "playerCards": {"card1": "As", "card2": "Kd"},
  "availableActions": [
    {"action": "bet", "isCorrect": true, "frequency": 0.80}
  ]
}
```

#### **Método 3: Interface Web (Futuro)**
```
┌─────────────────────────────────────┐
│          ADMIN PANEL                │
│                                     │
│ Cenário: [________________]         │
│ Street:  [Flop ▼]                  │
│ Posição: [BTN ▼]                   │
│                                     │
│ Cartas do Jogador:                  │
│ [As▼] [Kd▼]                        │
│                                     │
│ Ações Disponíveis:                  │
│ ☑ Fold    (20%) [____________]     │
│ ☑ Call    (30%) [____________]     │
│ ☑ Raise   (50%) [____________] ✓   │
│                                     │
│ [Salvar Cenário]                   │
└─────────────────────────────────────┘
```

---

## 📈 **ESCALABILIDADE E MELHORIAS**

### 🎯 **IMPLEMENTAÇÕES SUGERIDAS**

#### **1. Sistema de Categorias Dinâmicas**
```typescript
// Buscar cenários por categoria
const scenarios = await getScenariosByCategory('cbet_spots', 10)
const scenarios = await getScenariosByDifficulty('advanced', 5)
const scenarios = await getScenariosByPosition('BTN', 8)
```

#### **2. Cache e Performance**
```typescript
// Cache de cenários processados
const cachedScenarios = new Map<string, ProcessedScenario>()

export async function getCachedScenario(id: string) {
  if (cachedScenarios.has(id)) {
    return cachedScenarios.get(id)
  }
  
  const scenario = await processScenario(rawScenario)
  cachedScenarios.set(id, scenario)
  return scenario
}
```

#### **3. Validação de Cenários**
```typescript
function validateScenario(scenario: GTOScenarioFromDB): boolean {
  // Validar cartas
  const playerCards = JSON.parse(scenario.playerCards)
  if (!isValidCard(playerCards.card1) || !isValidCard(playerCards.card2)) {
    return false
  }
  
  // Validar ações
  const actions = JSON.parse(scenario.availableActions)
  const hasCorrectAction = actions.some(a => a.isCorrect)
  if (!hasCorrectAction) return false
  
  // Validar frequências somam ~1.0
  const totalFreq = actions.reduce((sum, a) => sum + a.frequency, 0)
  if (Math.abs(totalFreq - 1.0) > 0.1) return false
  
  return true
}
```

#### **4. Gerador de Cenários**
```typescript
class ScenarioGenerator {
  static generateCBetScenario(position: string, board: string[]) {
    return {
      id: `cbet_${position}_${board.join('')}`,
      street: 'flop',
      position,
      communityCards: { flop: board },
      availableActions: this.calculateCBetActions(position, board)
    }
  }
  
  static calculateCBetActions(position: string, board: string[]) {
    // Lógica GTO para calcular frequências
    const dryBoard = this.isDryBoard(board)
    const betFreq = dryBoard ? 0.75 : 0.45
    
    return [
      { action: 'check', frequency: 1 - betFreq, isCorrect: false },
      { action: 'bet', frequency: betFreq, isCorrect: true }
    ]
  }
}
```

---

## 🎮 **FLUXO COMPLETO - EXEMPLO PRÁTICO**

### **Cenário: "C-bet do BTN em Board Seco"**

```
1. BANCO DE DADOS
   ├── playerCards: '{"card1":"Kh","card2":"Qd"}'
   ├── communityCards: '{"flop":["As","7c","2h"]}'
   └── availableActions: '[{"action":"bet","isCorrect":true,"frequency":0.75}]'

2. SCENARIO SERVICE
   ├── JSON.parse() → Objetos JavaScript
   ├── convertCard("Kh") → {suit:"hearts", rank:"K", value:13}
   └── buildGameState() → GameState completo

3. API ROUTE
   ├── getRandomScenarios(5) → Array de cenários processados
   └── NextResponse.json() → Resposta HTTP

4. FRONTEND (Quiz)
   ├── fetch('/api/scenarios/demo') → Busca cenários
   ├── map(scenario => quizQuestion) → Converte para quiz
   └── setQuestions() → Atualiza estado

5. POKER TABLE
   ├── gameState → Renderiza mesa, cartas, pote
   ├── availableActions → Gera botões (Check, Bet)
   └── onAction → Captura decisão do usuário

6. FEEDBACK
   ├── Compara ação com correctAction
   ├── Mostra explanation se incorreto
   └── Atualiza estatísticas do usuário
```

---

## ✅ **RESUMO - MODULARIDADE ATUAL**

### **EXCELENTE:**
- ✅ Separação clara de responsabilidades
- ✅ Formato JSON flexível e extensível
- ✅ Processamento consistente de dados
- ✅ Interface padronizada

### **PODE MELHORAR:**
- 🔄 Sistema de cache para performance
- 🔄 Validação automática de cenários
- 🔄 Interface administrativa para criação
- 🔄 Categorização mais granular

### **FACILIDADE PARA NOVOS CENÁRIOS:**
- ✅ **Muito Fácil**: Adicionar ao script de população
- ✅ **Automático**: Sistema processa e disponibiliza
- ✅ **Zero Código**: Não precisa alterar frontend
- ✅ **Flexível**: Suporta qualquer situação de poker

**Status**: 🚀 **SISTEMA ALTAMENTE MODULAR E ESCALÁVEL!**
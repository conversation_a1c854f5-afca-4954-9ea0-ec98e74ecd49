'use client'

import { ChipStackProps } from '@/types'
import { cn, formatCurrency } from '@/lib/utils'
import { motion } from 'framer-motion'

export default function ChipStack({ amount, size = 'medium', className }: ChipStackProps) {
  const sizeClasses = {
    small: 'w-8 h-8 text-xs',
    medium: 'w-12 h-12 text-sm',
    large: 'w-16 h-16 text-base'
  }

  // Determinar cor do chip baseado no valor
  const getChipColor = (value: number) => {
    if (value >= 1000) return 'chip-black'
    if (value >= 100) return 'chip-green'
    if (value >= 25) return 'chip-blue'
    return 'chip-red'
  }

  // Calcular quantos chips mostrar na pilha
  const getChipCount = (value: number) => {
    if (value >= 1000) return Math.min(Math.floor(value / 1000), 5)
    if (value >= 100) return Math.min(Math.floor(value / 100), 5)
    if (value >= 25) return Math.min(Math.floor(value / 25), 5)
    return Math.min(Math.floor(value / 5), 5)
  }

  const chipColor = getChipColor(amount)
  const chipCount = Math.max(1, getChipCount(amount))

  return (
    <div className={cn('relative flex flex-col items-center', className)}>
      {/* Pilha de chips */}
      <div className="relative">
        {Array.from({ length: chipCount }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className={cn(
              'chip absolute flex items-center justify-center font-bold',
              chipColor,
              sizeClasses[size]
            )}
            style={{
              bottom: `${index * 2}px`,
              zIndex: chipCount - index
            }}
          >
            {index === chipCount - 1 && (
              <span className="text-white text-shadow">
                {amount >= 1000 ? '1K' : amount >= 100 ? '100' : amount >= 25 ? '25' : '5'}
              </span>
            )}
          </motion.div>
        ))}
      </div>

      {/* Valor total */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mt-2 text-center"
      >
        <span className="text-white text-sm font-semibold bg-black/50 px-2 py-1 rounded">
          {formatCurrency(amount)}
        </span>
      </motion.div>
    </div>
  )
}
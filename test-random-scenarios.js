const { getRandomScenarios } = require('./src/lib/scenario-service.ts');

async function testRandomScenarios() {
  console.log('🧪 Testando função getRandomScenarios...');
  
  try {
    const scenarios = await getRandomScenarios(3);
    
    console.log(`✅ Função executada com sucesso!`);
    console.log(`📊 Cenários retornados: ${scenarios.length}`);
    
    if (scenarios.length > 0) {
      console.log('\n📋 Cenários encontrados:');
      scenarios.forEach((scenario, index) => {
        console.log(`  ${index + 1}. ${scenario.name} (${scenario.gameState.street} - ${scenario.gameState.position.name})`);
      });
    } else {
      console.log('❌ Nenhum cenário foi retornado');
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar:', error.message);
  }
}

testRandomScenarios();
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Trophy, Star, Filter, Search } from 'lucide-react'
import Link from 'next/link'
import { useLanguage } from '@/hooks/useLanguage'
import AchievementCard from '@/components/achievements/AchievementCard'
import { Achievement, User } from '@/types'

export default function AchievementsPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [userAchievements, setUserAchievements] = useState<string[]>([])
  const [filter, setFilter] = useState<'all' | 'unlocked' | 'locked'>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Verificar autenticação
    const token = localStorage.getItem('token')
    const userData = localStorage.getItem('user')
    
    if (!token || !userData) {
      router.push('/auth/login')
      return
    }

    try {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)
      loadAchievements(parsedUser.id)
    } catch (error) {
      console.error('Erro ao carregar dados do usuário:', error)
      router.push('/auth/login')
    }
  }, [router])

  const loadAchievements = async (userId: string) => {
    try {
      setIsLoading(true)
      
      // Buscar todas as conquistas
      const achievementsResponse = await fetch('/api/achievements')
      const achievementsData = await achievementsResponse.json()
      
      // Buscar conquistas do usuário
      const userAchievementsResponse = await fetch(`/api/user/achievements/${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const userAchievementsData = await userAchievementsResponse.json()
      
      setAchievements(achievementsData)
      setUserAchievements(userAchievementsData.map((ua: any) => ua.achievementId))
    } catch (error) {
      console.error('Erro ao carregar conquistas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // Filtrar conquistas
  const filteredAchievements = achievements.filter(achievement => {
    const isUnlocked = userAchievements.includes(achievement.id)
    const matchesFilter = filter === 'all' || 
      (filter === 'unlocked' && isUnlocked) || 
      (filter === 'locked' && !isUnlocked)
    
    const matchesCategory = categoryFilter === 'all' || achievement.category === categoryFilter
    
    const matchesSearch = searchTerm === '' || 
      achievement.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      achievement.namePt.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesFilter && matchesCategory && matchesSearch
  })

  // Estatísticas das conquistas
  const unlockedCount = userAchievements.length
  const totalCount = achievements.length
  const completionPercentage = totalCount > 0 ? (unlockedCount / totalCount * 100).toFixed(1) : '0'

  // Categorias disponíveis
  const categories = Array.from(new Set(achievements.map(a => a.category)))

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">{t('achievements.loadingAchievements')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/dashboard" 
                className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Dashboard
              </Link>
              <div className="w-px h-6 bg-gray-600"></div>
              <div>
                <h1 className="text-2xl font-bold text-white flex items-center">
                  <Trophy className="w-7 h-7 mr-3 text-yellow-400" />
                  {t('achievements.title')}
                </h1>
                <p className="text-gray-400 text-sm">
                  {unlockedCount} {t('achievements.unlockedOf')} {totalCount} {t('achievements.achievementsUnlocked')} ({completionPercentage}%)
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Progresso geral */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">
                {t('achievements.achievementsProgress')}
              </h2>
              <p className="text-yellow-100">
                {t('achievements.continueTrainingUnlock')}
              </p>
            </div>
            <div className="text-right">
              <div className="text-4xl font-bold text-white">{completionPercentage}%</div>
              <div className="text-yellow-100 text-sm">{t('achievements.complete')}</div>
            </div>
          </div>
          
          <div className="w-full bg-yellow-800/30 rounded-full h-3">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 1.5, delay: 0.5 }}
              className="bg-white h-3 rounded-full"
            />
          </div>
        </motion.div>

        {/* Filtros */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-xl p-6 mb-8 border border-gray-700"
        >
          <div className="flex flex-col md:flex-row gap-4">
            {/* Filtro de status */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t('achievements.all')}</option>
                <option value="unlocked">{t('achievements.unlockedFilter')}</option>
                <option value="locked">{t('achievements.locked')}</option>
              </select>
            </div>

            {/* Filtro de categoria */}
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">{t('achievements.allCategories')}</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>

            {/* Busca */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('achievements.searchAchievements')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </motion.div>

        {/* Grid de conquistas */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {filteredAchievements.map((achievement, index) => (
            <motion.div
              key={achievement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <AchievementCard
                achievement={achievement}
                isUnlocked={userAchievements.includes(achievement.id)}
                progress={0.5} // TODO: Calcular progresso real
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Mensagem quando não há resultados */}
        {filteredAchievements.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              {t('achievements.noAchievementsFound')}
            </h3>
            <p className="text-gray-500">
              {t('achievements.adjustFilters')}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  )
}
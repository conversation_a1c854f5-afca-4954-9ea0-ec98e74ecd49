'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  Clock, 
  Target, 
  CheckCircle, 
  XCircle, 
  ArrowRight,
  Home,
  RotateCcw
} from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'
import PokerTable from '@/components/poker/PokerTable'
import { GameState, PokerAction, QuizQuestion, TrainingScenario } from '@/types'
import { getRandomScenarios } from '@/lib/scenario-service'
import toast from 'react-hot-toast'

export default function QuizPage() {
  const { t } = useLanguage()
  const router = useRouter()
  
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [questions, setQuestions] = useState<QuizQuestion[]>([])
  const [userAnswers, setUserAnswers] = useState<PokerAction[]>([])
  const [showResult, setShowResult] = useState(false)
  const [timeLeft, setTimeLeft] = useState(30)
  const [isLoading, setIsLoading] = useState(true)
  const [quizComplete, setQuizComplete] = useState(false)
  const [score, setScore] = useState(0)

  useEffect(() => {
    // Verificar autenticação
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/auth/login')
      return
    }

    generateQuizQuestions()
  }, [router])

  useEffect(() => {
    if (questions.length > 0 && !quizComplete && !showResult) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleTimeUp()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [currentQuestion, questions, quizComplete, showResult])

  const generateQuizQuestions = async () => {
    try {
      setIsLoading(true)
      
      console.log('🎯 Carregando cenários GTO REAIS do banco para o quiz...')
      
      // Buscar cenários reais do banco de dados via API
      const response = await fetch('/api/scenarios/demo')
      const data = await response.json()
      
      if (data.success && data.scenarios.length > 0) {
        console.log(`✅ ${data.scenarios.length} cenários GTO REAIS carregados do banco`)
        console.log('🚫 ZERO dados aleatórios - apenas situações reais de poker!')
        
        // Converter cenários para questões de quiz
        const quizQuestions: QuizQuestion[] = data.scenarios.map((scenario: any) => ({
          id: scenario.id,
          scenario: {
            id: scenario.id,
            name: scenario.name,
            description: scenario.description,
            gameState: {
              ...scenario.gameState,
              lastAction: undefined // Limpar ação anterior
            },
            gtoRecommendation: scenario.recommendation,
            difficulty: 'intermediate' as any,
            category: 'gto_training',
            tags: ['gto', 'real_scenario']
          },
          // USAR EXATAMENTE AS AÇÕES DISPONÍVEIS DO BANCO
          options: scenario.availableActions.map((action: any) => ({
            type: action.action,
            amount: action.amount,
            sizing: action.sizing || 'medium'
          })),
          correctAnswer: scenario.recommendation.action,
          explanation: scenario.recommendation.explanation,
          timeLimit: 30
        }))
        
        setQuestions(quizQuestions)
        console.log('✅ Quiz configurado com cenários GTO REAIS!')
        console.log('📋 Situações incluem: C-bet, 3-bet defense, bluff catch, opening ranges, combo draws')
      } else {
        console.error('❌ Nenhum cenário encontrado no banco')
        toast.error('Erro: Nenhum cenário disponível')
        setQuestions([])
      }
      
      setIsLoading(false)
    } catch (error) {
      console.error('❌ Erro ao carregar cenários para quiz:', error)
      toast.error('Erro ao carregar quiz')
      setQuestions([])
      setIsLoading(false)
    }
  }

  // REMOVIDO: Todo código de geração aleatória foi removido
  // O quiz agora usa APENAS cenários reais do banco de dados

  const handleAction = (action: PokerAction) => {
    if (showResult || quizComplete) return

    // Atualizar o gameState com a ação do jogador para feedback
    const updatedQuestions = [...questions]
    updatedQuestions[currentQuestion].scenario.gameState.lastAction = action
    setQuestions(updatedQuestions)

    const newAnswers = [...userAnswers, action]
    setUserAnswers(newAnswers)
    setShowResult(true)
    setTimeLeft(0)

    // Verificar se a resposta está correta
    const isCorrect = action.type === questions[currentQuestion].correctAnswer.type
    if (isCorrect) {
      setScore(prev => prev + 1)
      toast.success(t('results.correct'))
    } else {
      toast.error(t('results.incorrect'))
    }
  }

  const handleTimeUp = () => {
    if (showResult || quizComplete) return
    
    // Resposta automática por tempo esgotado
    const defaultAction: PokerAction = { type: 'fold' }
    handleAction(defaultAction)
  }

  const nextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1)
      setShowResult(false)
      setTimeLeft(30)
    } else {
      completeQuiz()
    }
  }

  const completeQuiz = async () => {
    setQuizComplete(true)
    
    try {
      // Salvar resultado do quiz
      const token = localStorage.getItem('token')
      const user = JSON.parse(localStorage.getItem('user') || '{}')
      
      const sessionData = {
        userId: user.id,
        sessionType: 'quiz',
        difficulty: 'intermediate',
        score,
        totalQuestions: questions.length,
        correctAnswers: score,
        xpGained: score * 10,
        duration: (questions.length * 30) - timeLeft,
        completed: true
      }

      await fetch('/api/training/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(sessionData)
      })

      toast.success(`Quiz completo! Você acertou ${score}/${questions.length} questões`)
    } catch (error) {
      console.error('Erro ao salvar sessão:', error)
    }
  }

  const restartQuiz = () => {
    setCurrentQuestion(0)
    setUserAnswers([])
    setShowResult(false)
    setTimeLeft(30)
    setQuizComplete(false)
    setScore(0)
    generateQuizQuestions()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Preparando seu quiz...</p>
        </div>
      </div>
    )
  }

  if (quizComplete) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gray-800 rounded-2xl p-8 max-w-md w-full text-center border border-gray-700"
        >
          <div className="mb-6">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Target className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {t('results.sessionComplete')}
            </h2>
            <p className="text-gray-400">
              Parabéns! Você completou o quiz.
            </p>
          </div>

          <div className="space-y-4 mb-8">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-3xl font-bold text-white mb-1">
                {score}/{questions.length}
              </div>
              <div className="text-gray-400 text-sm">Questões Corretas</div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-700 rounded-lg p-3">
                <div className="text-xl font-bold text-blue-400">
                  {((score / questions.length) * 100).toFixed(1)}%
                </div>
                <div className="text-gray-400 text-xs">Precisão</div>
              </div>
              <div className="bg-gray-700 rounded-lg p-3">
                <div className="text-xl font-bold text-green-400">
                  +{score * 10}
                </div>
                <div className="text-gray-400 text-xs">XP Ganho</div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <button
              onClick={restartQuiz}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
            >
              <RotateCcw className="w-5 h-5 mr-2" />
              Fazer Novo Quiz
            </button>
            
            <button
              onClick={() => router.push('/dashboard')}
              className="w-full bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
{t('training.backToDashboard')}
            </button>
          </div>
        </motion.div>
      </div>
    )
  }

  const currentQ = questions[currentQuestion]

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header do Quiz */}
        <div className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-white mb-1">
                Quiz GTO - Questão {currentQuestion + 1}/{questions.length}
              </h1>
              <p className="text-gray-400">
                {currentQ?.scenario.description}
              </p>
            </div>
            
            <div className="text-right">
              <div className="flex items-center text-white mb-2">
                <Clock className="w-5 h-5 mr-2" />
                <span className={`text-2xl font-bold ${timeLeft <= 10 ? 'text-red-400' : ''}`}>
                  {timeLeft}s
                </span>
              </div>
              <div className="text-gray-400 text-sm">
                Score: {score}/{currentQuestion + (showResult ? 1 : 0)}
              </div>
            </div>
          </div>
          
          {/* Barra de progresso */}
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
            />
          </div>
        </div>

        {/* Mesa de Poker */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestion}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
          >
            <PokerTable
              gameState={currentQ?.scenario.gameState}
              onAction={handleAction}
              showRecommendation={showResult}
              recommendation={showResult ? currentQ?.scenario.gtoRecommendation : undefined}
              availableActions={currentQ?.options.map(option => ({
                action: option.type,
                amount: option.amount,
                sizing: option.sizing,
                isCorrect: option.type === currentQ?.correctAnswer.type,
                frequency: option.type === currentQ?.correctAnswer.type ? 0.85 : 0.15
              }))}
              onNewSituation={() => nextQuestion()}
              onTryAgain={() => {
                setShowResult(false)
                setTimeLeft(30)
                // Limpar a última ação para permitir nova tentativa
                const updatedQuestions = [...questions]
                updatedQuestions[currentQuestion].scenario.gameState.lastAction = undefined
                setQuestions(updatedQuestions)
              }}
            />
          </motion.div>
        </AnimatePresence>

        {/* Resultado da questão */}
        <AnimatePresence>
          {showResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mt-6 bg-gray-800 rounded-xl p-6 border border-gray-700"
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {userAnswers[currentQuestion]?.type === currentQ?.correctAnswer.type ? (
                    <CheckCircle className="w-8 h-8 text-green-400" />
                  ) : (
                    <XCircle className="w-8 h-8 text-red-400" />
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2">
                    {userAnswers[currentQuestion]?.type === currentQ?.correctAnswer.type 
                      ? t('results.correct') 
                      : t('results.incorrect')
                    }
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-gray-400 text-sm mb-1">{t('results.yourAnswer')}</p>
                      <p className="text-white font-semibold capitalize">
                        {userAnswers[currentQuestion]?.type}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm mb-1">{t('results.correctAnswer')}</p>
                      <p className="text-green-400 font-semibold capitalize">
                        {currentQ?.correctAnswer.type}
                      </p>
                    </div>
                  </div>
                  
                  <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4 mb-4">
                    <h4 className="text-blue-300 font-semibold mb-2">{t('results.explanation')}</h4>
                    <p className="text-gray-300 text-sm">
                      {currentQ?.explanation}
                    </p>
                  </div>
                  
                  <button
                    onClick={nextQuestion}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center"
                  >
                    {currentQuestion < questions.length - 1 ? t('results.nextQuestion') : 'Finalizar Quiz'}
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
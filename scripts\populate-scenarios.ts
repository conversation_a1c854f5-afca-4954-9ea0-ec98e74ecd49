import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Cenários GTO reais e detalhados para o quiz
const realGTOScenarios = [
  {
    id: "scenario_001_btn_cbet_dry_board",
    name: "BTN C-bet on Dry Board",
    nameEn: "BTN C-bet on Dry Board",
    namePt: "C-bet do BTN em Board Seco",
    description: "Button continuation bet on A-7-2 rainbow after opening preflop",
    descriptionEn: "Button continuation bet on A-7-2 rainbow after opening preflop",
    descriptionPt: "Continuation bet do Button em A-7-2 rainbow após abertura pré-flop",
    gameType: "cash",
    street: "flop",
    position: "BTN",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Kh", card2: "Qd" }),
    communityCards: JSON.stringify({ flop: ["As", "7c", "2h"] }),
    potSize: 15.0,
    playerStack: 194.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "fold", isActive: false, stackSize: 200 },
      { position: "CO", action: "fold", isActive: false, stackSize: 200 },
      { position: "SB", action: "fold", isActive: false, stackSize: 199 },
      { position: "BB", action: "call", amount: 4, isActive: true, stackSize: 194 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.05,
        explanation: "Foldar com overcards e posição é extremamente passivo.",
        explanationEn: "Folding with overcards and position is extremely passive.",
        explanationPt: "Foldar com overcards e posição é extremamente passivo."
      },
      {
        action: "check",
        isCorrect: false,
        frequency: 0.20,
        explanation: "Check desperdiça vantagem de range em board seco.",
        explanationEn: "Checking wastes range advantage on dry board.",
        explanationPt: "Check desperdiça vantagem de range em board seco."
      },
      {
        action: "bet",
        amount: 10,
        sizing: "medium",
        isCorrect: true,
        frequency: 0.75,
        explanation: "C-bet padrão em board seco com vantagem de range.",
        explanationEn: "Standard c-bet on dry board with range advantage.",
        explanationPt: "C-bet padrão em board seco com vantagem de range."
      }
    ]),
    correctAction: "bet",
    equity: 0.35,
    frequency: 0.75,
    explanation: "Board A-7-2 rainbow favorece o range do agressor pré-flop. Com KQ, temos overcards e posição. C-bet 2/3 pot é padrão para extrair valor e fazer mãos piores foldarem.",
    explanationEn: "A-7-2 rainbow board favors the preflop aggressor's range. With KQ, we have overcards and position. 2/3 pot c-bet is standard to extract value and fold out worse hands.",
    explanationPt: "Board A-7-2 rainbow favorece o range do agressor pré-flop. Com KQ, temos overcards e posição. C-bet 2/3 pot é padrão para extrair valor e fazer mãos piores foldarem.",
    context: JSON.stringify({
      spr: 12.9,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "dry",
      handStrength: "medium"
    }),
    difficulty: "intermediate",
    category: "cbet",
    tags: JSON.stringify(["position_play", "continuation_bet", "dry_board", "overcards"])
  },

  {
    id: "scenario_002_bb_3bet_defense",
    name: "BB vs CO 3-bet with AJo",
    nameEn: "BB vs CO 3-bet with AJo",
    namePt: "BB vs 3-bet do CO com AJo",
    description: "Big blind facing 3-bet from cutoff with AJ offsuit",
    descriptionEn: "Big blind facing 3-bet from cutoff with AJ offsuit",
    descriptionPt: "Big blind enfrentando 3-bet do cutoff com AJ offsuit",
    gameType: "cash",
    street: "preflop",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Ah", card2: "Jc" }),
    communityCards: JSON.stringify({}),
    potSize: 37.0,
    playerStack: 182.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "fold", isActive: false, stackSize: 200 },
      { position: "CO", action: "raise", amount: 18, isActive: true, stackSize: 182 },
      { position: "BTN", action: "fold", isActive: false, stackSize: 200 },
      { position: "SB", action: "fold", isActive: false, stackSize: 199 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: true,
        frequency: 0.70,
        explanation: "AJo está no fundo do range de call vs 3-bet do CO. Fold é padrão.",
        explanationEn: "AJo is at the bottom of calling range vs CO 3-bet. Folding is standard.",
        explanationPt: "AJo está no fundo do range de call vs 3-bet do CO. Fold é padrão."
      },
      {
        action: "call",
        amount: 16,
        isCorrect: false,
        frequency: 0.25,
        explanation: "Call é marginal. Ficará fora de posição com mão que não joga bem.",
        explanationEn: "Calling is marginal. Will be out of position with hand that doesn't play well.",
        explanationPt: "Call é marginal. Ficará fora de posição com mão que não joga bem."
      },
      {
        action: "raise",
        amount: 54,
        sizing: "large",
        isCorrect: false,
        frequency: 0.05,
        explanation: "4-bet com AJo é muito solto. Reserve para mãos premium.",
        explanationEn: "4-betting AJo is too loose. Save for premium hands.",
        explanationPt: "4-bet com AJo é muito solto. Reserve para mãos premium."
      }
    ]),
    correctAction: "fold",
    equity: 0.42,
    frequency: 0.70,
    explanation: "AJo vs 3-bet do CO é spot marginal. Apesar das pot odds, ficamos fora de posição com mão que realiza mal sua equidade. Fold é mais lucrativo a longo prazo.",
    explanationEn: "AJo vs CO 3-bet is marginal spot. Despite pot odds, we're out of position with hand that realizes equity poorly. Folding is more profitable long-term.",
    explanationPt: "AJo vs 3-bet do CO é spot marginal. Apesar das pot odds, ficamos fora de posição com mão que realiza mal sua equidade. Fold é mais lucrativo a longo prazo.",
    context: JSON.stringify({
      spr: 10.1,
      hasAggression: true,
      lastAggressor: "CO",
      boardTexture: "preflop",
      handStrength: "medium"
    }),
    difficulty: "advanced",
    category: "3bet_defense",
    tags: JSON.stringify(["preflop", "3bet_defense", "position_disadvantage", "marginal_hand"])
  },

  {
    id: "scenario_003_river_bluff_catch",
    name: "River Bluff Catch Decision",
    nameEn: "River Bluff Catch Decision",
    namePt: "Decisão de Bluff Catch no River",
    description: "Facing large river bet with trips - bluff catch spot",
    descriptionEn: "Facing large river bet with trips - bluff catch spot",
    descriptionPt: "Enfrentando aposta grande no river com trinca - spot de bluff catch",
    gameType: "cash",
    street: "river",
    position: "BB",
    stackDepth: "medium",
    playerCards: JSON.stringify({ card1: "Jh", card2: "Js" }),
    communityCards: JSON.stringify({
      flop: ["Kc", "Jd", "7s"],
      turn: "4h",
      river: "Ac"
    }),
    potSize: 240.0,
    playerStack: 80.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 120, isActive: true, stackSize: 80 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.35,
        explanation: "Foldar trinca é muito apertado contra range que inclui bluffs.",
        explanationEn: "Folding trips is too tight against range that includes bluffs.",
        explanationPt: "Foldar trinca é muito apertado contra range que inclui bluffs."
      },
      {
        action: "call",
        amount: 120,
        isCorrect: true,
        frequency: 0.65,
        explanation: "Bluff catch com trinca. Ás no river é carta perfeita para bluffs.",
        explanationEn: "Bluff catch with trips. River Ace is perfect bluff card.",
        explanationPt: "Bluff catch com trinca. Ás no river é carta perfeita para bluffs."
      }
    ]),
    correctAction: "call",
    equity: 0.55,
    frequency: 0.65,
    explanation: "Com JJ em K-J-7-4-A, temos trinca de valetes. O Ás no river completa muitos draws perdidos e é carta perfeita para bluffs. Range do oponente inclui muitos bluffs após linha passiva.",
    explanationEn: "With JJ on K-J-7-4-A, we have trip jacks. River Ace completes many missed draws and is perfect bluff card. Opponent's range includes many bluffs after passive line.",
    explanationPt: "Com JJ em K-J-7-4-A, temos trinca de valetes. O Ás no river completa muitos draws perdidos e é carta perfeita para bluffs. Range do oponente inclui muitos bluffs após linha passiva.",
    context: JSON.stringify({
      spr: 0.33,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "coordinated",
      handStrength: "strong"
    }),
    difficulty: "advanced",
    category: "bluff_catch",
    tags: JSON.stringify(["bluff_catch", "river_play", "trips", "large_bet"])
  },

  {
    id: "scenario_004_co_squeeze_spot",
    name: "CO Squeeze vs MP Open + BTN Call",
    nameEn: "CO Squeeze vs MP Open + BTN Call",
    namePt: "CO Squeeze vs Abertura MP + Call BTN",
    description: "Cutoff squeeze opportunity with A5s after MP opens and BTN calls",
    descriptionEn: "Cutoff squeeze opportunity with A5s after MP opens and BTN calls",
    descriptionPt: "Oportunidade de squeeze do CO com A5s após MP abrir e BTN pagar",
    gameType: "cash",
    street: "preflop",
    position: "CO",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "As", card2: "5s" }),
    communityCards: JSON.stringify({}),
    potSize: 15.0,
    playerStack: 194.0,
    opponentActions: JSON.stringify([
      { position: "UTG", action: "fold", isActive: false, stackSize: 200 },
      { position: "MP", action: "raise", amount: 6, isActive: true, stackSize: 194 },
      { position: "BTN", action: "call", amount: 6, isActive: true, stackSize: 194 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.60,
        explanation: "Fold desperdiça oportunidade de squeeze com mão adequada.",
        explanationEn: "Folding wastes squeeze opportunity with suitable hand.",
        explanationPt: "Fold desperdiça oportunidade de squeeze com mão adequada."
      },
      {
        action: "call",
        amount: 6,
        isCorrect: false,
        frequency: 0.15,
        explanation: "Call cria pote multiway fora de posição.",
        explanationEn: "Calling creates multiway pot out of position.",
        explanationPt: "Call cria pote multiway fora de posição."
      },
      {
        action: "raise",
        amount: 22,
        sizing: "large",
        isCorrect: true,
        frequency: 0.25,
        explanation: "Squeeze ideal com A5s - blockers de Ás e boa jogabilidade.",
        explanationEn: "Perfect squeeze with A5s - ace blockers and good playability.",
        explanationPt: "Squeeze ideal com A5s - blockers de Ás e boa jogabilidade."
      }
    ]),
    correctAction: "raise",
    equity: 0.38,
    frequency: 0.25,
    explanation: "A5s é mão ideal para squeeze: tem blockers de Ás (reduz combos de AA/AK dos oponentes), boa jogabilidade pós-flop e pode fazer os dois oponentes foldarem frequentemente.",
    explanationEn: "A5s is ideal squeeze hand: has ace blockers (reduces opponents' AA/AK combos), good postflop playability and can make both opponents fold frequently.",
    explanationPt: "A5s é mão ideal para squeeze: tem blockers de Ás (reduz combos de AA/AK dos oponentes), boa jogabilidade pós-flop e pode fazer os dois oponentes foldarem frequentemente.",
    context: JSON.stringify({
      spr: 8.8,
      hasAggression: false,
      lastAggressor: "MP",
      boardTexture: "preflop",
      handStrength: "medium"
    }),
    difficulty: "advanced",
    category: "squeeze",
    tags: JSON.stringify(["preflop", "squeeze", "suited_ace", "position_play"])
  },

  {
    id: "scenario_005_turn_check_raise",
    name: "Turn Check-Raise Bluff",
    nameEn: "Turn Check-Raise Bluff",
    namePt: "Check-Raise Bluff no Turn",
    description: "Check-raise bluff opportunity on turn with combo draw",
    descriptionEn: "Check-raise bluff opportunity on turn with combo draw",
    descriptionPt: "Oportunidade de check-raise bluff no turn com combo draw",
    gameType: "cash",
    street: "turn",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "9h", card2: "8h" }),
    communityCards: JSON.stringify({
      flop: ["7h", "6c", "2d"],
      turn: "5s"
    }),
    potSize: 45.0,
    playerStack: 170.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 30, isActive: true, stackSize: 164 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.10,
        explanation: "Fold desperdiça mão com muita equidade e outs.",
        explanationEn: "Folding wastes hand with lots of equity and outs.",
        explanationPt: "Fold desperdiça mão com muita equidade e outs."
      },
      {
        action: "call",
        amount: 30,
        isCorrect: false,
        frequency: 0.50,
        explanation: "Call é passivo demais com tanto equity.",
        explanationEn: "Calling is too passive with so much equity.",
        explanationPt: "Call é passivo demais com tanto equity."
      },
      {
        action: "raise",
        amount: 90,
        sizing: "large",
        isCorrect: true,
        frequency: 0.40,
        explanation: "Check-raise com combo draw - 13 outs para nuts ou quase nuts.",
        explanationEn: "Check-raise with combo draw - 13 outs to nuts or near-nuts.",
        explanationPt: "Check-raise com combo draw - 13 outs para nuts ou quase nuts."
      }
    ]),
    correctAction: "raise",
    equity: 0.52,
    frequency: 0.40,
    explanation: "Com 98h em 7-6-2-5, temos straight draw (4 e T) + flush draw + overcards = 13 outs. Check-raise é agressivo mas correto com tanta equidade. Pode fazer oponente foldar e ainda temos boa equidade se pago.",
    explanationEn: "With 98h on 7-6-2-5, we have straight draw (4 and T) + flush draw + overcards = 13 outs. Check-raise is aggressive but correct with so much equity. Can make opponent fold and still have good equity if called.",
    explanationPt: "Com 98h em 7-6-2-5, temos straight draw (4 e T) + flush draw + overcards = 13 outs. Check-raise é agressivo mas correto com tanta equidade. Pode fazer oponente foldar e ainda temos boa equidade se pago.",
    context: JSON.stringify({
      spr: 3.6,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "coordinated",
      handStrength: "draw"
    }),
    difficulty: "intermediate",
    category: "check_raise",
    tags: JSON.stringify(["turn_play", "check_raise", "combo_draw", "semi_bluff"])
  },

  // CENÁRIOS ADICIONAIS PARA QUIZ MAIS VARIADO

  {
    id: "scenario_006_utg_opening_range",
    name: "UTG Opening Decision with TT",
    nameEn: "UTG Opening Decision with TT",
    namePt: "Decisão de Abertura UTG com TT",
    description: "UTG opening decision with pocket tens",
    descriptionEn: "UTG opening decision with pocket tens",
    descriptionPt: "Decisão de abertura UTG com par de dez",
    gameType: "cash",
    street: "preflop",
    position: "UTG",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Th", card2: "Ts" }),
    communityCards: JSON.stringify({}),
    potSize: 3.0,
    playerStack: 200.0,
    opponentActions: JSON.stringify([]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.05,
        explanation: "Foldar TT em UTG é muito apertado. É uma mão premium.",
        explanationEn: "Folding TT in UTG is too tight. It's a premium hand.",
        explanationPt: "Foldar TT em UTG é muito apertado. É uma mão premium."
      },
      {
        action: "call",
        amount: 2,
        isCorrect: false,
        frequency: 0.10,
        explanation: "Limp com TT desperdiça valor. Você deve raise.",
        explanationEn: "Limping TT wastes value. You should raise.",
        explanationPt: "Limp com TT desperdiça valor. Você deve raise."
      },
      {
        action: "raise",
        amount: 6,
        sizing: "medium",
        isCorrect: true,
        frequency: 0.85,
        explanation: "TT é abertura padrão em UTG. Raise 3x para extrair valor.",
        explanationEn: "TT is standard UTG open. Raise 3x to extract value.",
        explanationPt: "TT é abertura padrão em UTG. Raise 3x para extrair valor."
      }
    ]),
    correctAction: "raise",
    equity: 0.65,
    frequency: 0.85,
    explanation: "TT é uma mão premium que deve ser aberta em UTG. Raise 3x é sizing padrão para construir o pote e definir ranges pós-flop.",
    explanationEn: "TT is a premium hand that should be opened UTG. 3x raise is standard sizing to build pot and define postflop ranges.",
    explanationPt: "TT é uma mão premium que deve ser aberta em UTG. Raise 3x é sizing padrão para construir o pote e definir ranges pós-flop.",
    context: JSON.stringify({
      spr: 33.3,
      hasAggression: false,
      lastAggressor: null,
      boardTexture: "preflop",
      handStrength: "strong"
    }),
    difficulty: "beginner",
    category: "opening_ranges",
    tags: JSON.stringify(["preflop", "utg", "pocket_pair", "opening_range"])
  },

  {
    id: "scenario_007_sb_vs_bb_headsup",
    name: "SB vs BB Heads Up with K9o",
    nameEn: "SB vs BB Heads Up with K9o",
    namePt: "SB vs BB Heads Up com K9o",
    description: "Small blind decision heads up with K9o",
    descriptionEn: "Small blind decision heads up with K9o",
    descriptionPt: "Decisão do small blind heads up com K9o",
    gameType: "cash",
    street: "preflop",
    position: "SB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Kh", card2: "9c" }),
    communityCards: JSON.stringify({}),
    potSize: 3.0,
    playerStack: 199.0,
    opponentActions: JSON.stringify([]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.25,
        explanation: "K9o é forte o suficiente para abrir SB vs BB.",
        explanationEn: "K9o is strong enough to open SB vs BB.",
        explanationPt: "K9o é forte o suficiente para abrir SB vs BB."
      },
      {
        action: "call",
        amount: 1,
        isCorrect: false,
        frequency: 0.15,
        explanation: "Limp no SB é fraco e permite que BB realize equity.",
        explanationEn: "Limping SB is weak and allows BB to realize equity.",
        explanationPt: "Limp no SB é fraco e permite que BB realize equity."
      },
      {
        action: "raise",
        amount: 6,
        sizing: "medium",
        isCorrect: true,
        frequency: 0.60,
        explanation: "K9o deve ser aberto no SB vs BB. Aplique pressão.",
        explanationEn: "K9o should be opened SB vs BB. Apply pressure.",
        explanationPt: "K9o deve ser aberto no SB vs BB. Aplique pressão."
      }
    ]),
    correctAction: "raise",
    equity: 0.58,
    frequency: 0.60,
    explanation: "Em heads-up SB vs BB, K9o está no range de abertura. Raise para aplicar pressão e tomar iniciativa na mão.",
    explanationEn: "In heads-up SB vs BB, K9o is in opening range. Raise to apply pressure and take initiative in the hand.",
    explanationPt: "Em heads-up SB vs BB, K9o está no range de abertura. Raise para aplicar pressão e tomar iniciativa na mão.",
    context: JSON.stringify({
      spr: 33.2,
      hasAggression: false,
      lastAggressor: null,
      boardTexture: "preflop",
      handStrength: "medium"
    }),
    difficulty: "intermediate",
    category: "sb_vs_bb",
    tags: JSON.stringify(["preflop", "sb", "heads_up", "opening_range"])
  },

  {
    id: "scenario_008_flop_check_call",
    name: "Flop Check-Call with Flush Draw",
    nameEn: "Flop Check-Call with Flush Draw",
    namePt: "Check-Call no Flop com Flush Draw",
    description: "BB check-call decision on coordinated flop with nut flush draw",
    descriptionEn: "BB check-call decision on coordinated flop with nut flush draw",
    descriptionPt: "Decisão de check-call do BB em flop coordenado com nut flush draw",
    gameType: "cash",
    street: "flop",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({ card1: "Ad", card2: "5d" }),
    communityCards: JSON.stringify({ flop: ["9d", "8h", "7c"] }),
    potSize: 15.0,
    playerStack: 194.0,
    opponentActions: JSON.stringify([
      { position: "BTN", action: "bet", amount: 10, isActive: true, stackSize: 190 }
    ]),
    availableActions: JSON.stringify([
      {
        action: "fold",
        isCorrect: false,
        frequency: 0.30,
        explanation: "Fold com nut flush draw é muito apertado.",
        explanationEn: "Folding nut flush draw is too tight.",
        explanationPt: "Fold com nut flush draw é muito apertado."
      },
      {
        action: "call",
        amount: 10,
        isCorrect: true,
        frequency: 0.55,
        explanation: "Call é correto com nut flush draw e overcards.",
        explanationEn: "Call is correct with nut flush draw and overcards.",
        explanationPt: "Call é correto com nut flush draw e overcards."
      },
      {
        action: "raise",
        amount: 35,
        sizing: "large",
        isCorrect: false,
        frequency: 0.15,
        explanation: "Raise é muito agressivo com apenas um draw.",
        explanationEn: "Raising is too aggressive with just a draw.",
        explanationPt: "Raise é muito agressivo com apenas um draw."
      }
    ]),
    correctAction: "call",
    equity: 0.42,
    frequency: 0.55,
    explanation: "Com Ad5d em 9d-8h-7c, temos nut flush draw (9 outs) + overcard (3 outs) = 12 outs. Call é correto para ver o turn com boa equidade.",
    explanationEn: "With Ad5d on 9d-8h-7c, we have nut flush draw (9 outs) + overcard (3 outs) = 12 outs. Call is correct to see turn with good equity.",
    explanationPt: "Com Ad5d em 9d-8h-7c, temos nut flush draw (9 outs) + overcard (3 outs) = 12 outs. Call é correto para ver o turn com boa equidade.",
    context: JSON.stringify({
      spr: 19.4,
      hasAggression: true,
      lastAggressor: "BTN",
      boardTexture: "coordinated",
      handStrength: "draw"
    }),
    difficulty: "intermediate",
    category: "draw_play",
    tags: JSON.stringify(["flop_play", "flush_draw", "check_call", "bb_defense"])
  }
]

async function main() {
  console.log('🎯 Populando cenários GTO reais para o quiz...')

  for (const scenario of realGTOScenarios) {
    await prisma.gTOScenario.upsert({
      where: { id: scenario.id },
      update: scenario,
      create: scenario
    })
    console.log(`✅ Cenário criado: ${scenario.namePt}`)
  }

  console.log('\n📊 Resumo dos cenários populados:')
  console.log('1. BTN C-bet em board seco (Intermediate)')
  console.log('2. BB vs 3-bet do CO com AJo (Advanced)')
  console.log('3. River bluff catch com JJ (Advanced)')
  console.log('4. CO squeeze com A5s (Advanced)')
  console.log('5. Turn check-raise com combo draw (Intermediate)')
  console.log('6. UTG opening com TT (Beginner)')
  console.log('7. SB vs BB heads-up com K9o (Intermediate)')
  console.log('8. Flop check-call com flush draw (Intermediate)')

  console.log('\n✅ 8 cenários GTO reais populados com sucesso!')
  console.log('🎮 Quiz agora usa apenas dados reais do banco!')
  console.log('📋 Cenários cobrem: preflop, flop, turn, river')
  console.log('🎯 Dificuldades: beginner, intermediate, advanced')
  console.log('🏷️ Categorias: cbet, 3bet_defense, bluff_catch, squeeze, check_raise, opening_ranges, sb_vs_bb, draw_play')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
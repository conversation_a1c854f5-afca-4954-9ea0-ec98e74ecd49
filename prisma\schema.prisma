// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  language  String   @default("pt")
  level     Int      @default(1)
  xp        Int      @default(0)
  totalPoints Int    @default(0)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  sessions    TrainingSession[]
  hands       HandHistory[]
  achievements UserAchievement[]
  statistics  UserStatistics?

  @@map("users")
}

model TrainingSession {
  id          String   @id @default(cuid())
  userId      String
  sessionType String   // "quiz", "simulation", "practice"
  difficulty  String   // "beginner", "intermediate", "advanced"
  score       Int      @default(0)
  totalQuestions Int   @default(0)
  correctAnswers Int   @default(0)
  xpGained    Int      @default(0)
  duration    Int      // em segundos
  completed   Boolean  @default(false)
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("training_sessions")
}

model HandHistory {
  id            String   @id @default(cuid())
  userId        String
  scenarioId    String?  // ID do cenário GTO usado
  gameType      String   // "cash", "tournament"
  position      String   // "UTG", "MP", "CO", "BTN", "SB", "BB"
  holeCards     String   // JSON string: ["As", "Kh"]
  communityCards String? // JSON string: ["Ah", "Kd", "Qc", "Jh", "Ts"]
  potSize       Float
  stackSize     Float
  action        String   // "fold", "call", "raise", "check", "bet"
  betSize       Float?
  isCorrect     Boolean
  gtoAction     String   // ação recomendada pelo GTO
  gtoBetSize    Float?
  gtoFrequency  Float?   // frequência GTO da ação recomendada
  equity        Float?   // equidade da mão
  explanation   String?  // explicação da jogada
  street        String   // "preflop", "flop", "turn", "river"
  sessionType   String   // "demo", "quiz", "simulation", "practice"
  responseTime  Int?     // tempo de resposta em segundos
  createdAt     DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("hand_history")
}

model Achievement {
  id          String @id @default(cuid())
  name        String @unique
  nameEn      String
  namePt      String
  description String
  descriptionEn String
  descriptionPt String
  icon        String
  xpReward    Int
  category    String // "training", "accuracy", "streak", "level"
  requirement String // JSON com critérios

  users UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String
  achievementId String
  unlockedAt    DateTime @default(now())

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model UserStatistics {
  id                    String @id @default(cuid())
  userId                String @unique
  totalHands            Int    @default(0)
  correctDecisions      Int    @default(0)
  averageEquity         Float  @default(0.0)
  preflopAccuracy       Float  @default(0.0)
  flopAccuracy          Float  @default(0.0)
  turnAccuracy          Float  @default(0.0)
  riverAccuracy         Float  @default(0.0)
  aggressionFrequency   Float  @default(0.0)
  foldFrequency         Float  @default(0.0)
  callFrequency         Float  @default(0.0)
  raiseFrequency        Float  @default(0.0)
  cBetFrequency         Float  @default(0.0)
  checkRaiseFrequency   Float  @default(0.0)
  currentStreak         Int    @default(0)
  longestStreak         Int    @default(0)
  updatedAt             DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_statistics")
}

model GTOScenario {
  id              String @id @default(cuid())
  name            String
  nameEn          String
  namePt          String
  description     String
  descriptionEn   String
  descriptionPt   String
  gameType        String // "cash", "tournament"
  street          String // "preflop", "flop", "turn", "river"
  position        String
  stackDepth      String // "shallow", "medium", "deep"
  
  // Cartas da situação
  playerCards     String @db.Text // JSON: {"card1": "Ah", "card2": "Kd"}
  communityCards  String @db.Text // JSON: {"flop": ["9h", "7c", "2d"], "turn": "Qs", "river": "4h"}
  
  // Estado da mesa
  potSize         Float
  playerStack     Float
  opponentActions String @db.Text // JSON com ações dos oponentes
  
  // Ações disponíveis
  availableActions String @db.Text // JSON com todas as opções e suas consequências
  correctAction   String // ação GTO recomendada
  
  // Métricas GTO
  equity          Float
  frequency       Float  // frequência da ação
  explanation     String
  explanationEn   String
  explanationPt   String
  
  // Contexto adicional
  context         String // JSON com informações contextuais
  
  // Classificação
  difficulty      String // "beginner", "intermediate", "advanced"
  category        String // "3bet", "cbet", "checkraise", etc.
  tags            String // JSON array de tags
  
  // Filtro personalizado
  customFilterId  String?
  customFilter    CustomFilter? @relation(fields: [customFilterId], references: [id])
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("gto_scenarios")
}

model CustomFilter {
  id          String @id @default(cuid())
  name        String @unique
  description String?
  color       String? // Cor para identificação visual
  
  // Relacionamentos
  scenarios   GTOScenario[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("custom_filters")
}
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  ArrowLeft, 
  BarChart3, 
  TrendingUp, 
  Calendar,
  Filter,
  Download
} from 'lucide-react'
import Link from 'next/link'
import { useLanguage } from '@/hooks/useLanguage'
import UserStats from '@/components/dashboard/UserStats'
import { User } from '@/types'

export default function StatisticsPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [timeFilter, setTimeFilter] = useState<'week' | 'month' | 'all'>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  useEffect(() => {
    loadDefaultUser()
  }, [])

  const loadDefaultUser = async () => {
    try {
      const defaultUserId = 'cmdhptt1n0000m0rwt9dn15fu'
      const response = await fetch(`/api/user/profile/${defaultUserId}`)
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/dashboard" 
                className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Dashboard
              </Link>
              <div className="w-px h-6 bg-gray-600"></div>
              <div>
                <h1 className="text-2xl font-bold text-white flex items-center">
                  <BarChart3 className="w-7 h-7 mr-3 text-blue-400" />
                  {t('statistics.detailedStatistics')}
                </h1>
                <p className="text-gray-400 text-sm">
                  {t('statistics.completeAnalysis')}
                </p>
              </div>
            </div>
            
            {/* Filtros */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-400" />
                <select
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value as any)}
                  className="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="week">{t('statistics.lastWeek')}</option>
                  <option value="month">{t('statistics.lastMonth')}</option>
                  <option value="all">{t('statistics.allTime')}</option>
                </select>
              </div>
              
              <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <Download className="w-4 h-4" />
                <span>{t('statistics.export')}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Informações do usuário */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">
                {user.username}
              </h2>
              <div className="flex items-center space-x-6 text-blue-100">
                <div className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  <span>{t('statistics.level')} {user.level}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  <span>{t('statistics.memberSince')} {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-3xl font-bold text-white">{user.xp}</div>
              <div className="text-blue-100 text-sm">{t('statistics.totalXP')}</div>
            </div>
          </div>
        </motion.div>

        {/* Estatísticas detalhadas */}
        <UserStats userId={user.id} />

        {/* Análise de tendências */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-8 bg-gray-800 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center mb-6">
            <TrendingUp className="w-5 h-5 text-green-400 mr-2" />
            <h3 className="text-lg font-semibold text-white">{t('statistics.trendAnalysis')}</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">{t('statistics.strengths')}</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                {(t('statistics.strengthsItems') as unknown as string[]).map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </div>
            
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">{t('statistics.areasForImprovement')}</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                {(t('statistics.improvementItems') as unknown as string[]).map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </div>
            
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">{t('statistics.recommendations')}</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                {(t('statistics.recommendationItems') as unknown as string[]).map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Comparação com outros jogadores */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-8 bg-gray-800 rounded-xl p-6 border border-gray-700"
        >
          <div className="flex items-center mb-6">
            <BarChart3 className="w-5 h-5 text-purple-400 mr-2" />
            <h3 className="text-lg font-semibold text-white">{t('statistics.globalComparison')}</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-white font-medium mb-4">{t('statistics.yourRanking')}</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.overallAccuracy')}</span>
                  <span className="text-green-400">Top 25%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.totalXP')}</span>
                  <span className="text-blue-400">Top 40%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.maxStreak')}</span>
                  <span className="text-yellow-400">Top 30%</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">{t('statistics.communityAverages')}</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.averageAccuracy')}</span>
                  <span className="text-gray-400">72.3%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.averageXP')}</span>
                  <span className="text-gray-400">1,250</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{t('statistics.handsPerSession')}</span>
                  <span className="text-gray-400">15.7</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
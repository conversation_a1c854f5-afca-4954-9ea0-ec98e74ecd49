'use client'

import { Card as CardType, CardProps } from '@/types'
import { SUIT_SYMBOLS, SUIT_COLORS } from '@/lib/constants'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

export default function Card({ card, faceUp = true, size = 'medium', className }: CardProps) {
  const sizeClasses = {
    small: 'w-12 h-16',
    medium: 'w-16 h-20',
    large: 'w-20 h-28'
  }

  const textSizes = {
    small: { rank: 'text-xs', suit: 'text-sm', center: 'text-lg' },
    medium: { rank: 'text-sm', suit: 'text-base', center: 'text-2xl' },
    large: { rank: 'text-base', suit: 'text-lg', center: 'text-3xl' }
  }

  if (!faceUp) {
    return (
      <motion.div
        initial={{ rotateY: 0 }}
        animate={{ rotateY: 0 }}
        className={cn(
          'relative rounded-lg shadow-lg border-2 flex items-center justify-center',
          'bg-gradient-to-br from-blue-800 via-blue-900 to-blue-950',
          'border-blue-700',
          sizeClasses[size],
          className
        )}
        style={{ aspectRatio: '5/7' }}
      >
        {/* Padrão do verso da carta */}
        <div className="absolute inset-1 rounded border border-blue-600 bg-gradient-to-br from-blue-700 to-blue-800">
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-blue-400 rounded-full bg-blue-600 flex items-center justify-center">
              <div className="w-4 h-4 bg-blue-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ rotateY: 180 }}
      animate={{ rotateY: 0 }}
      transition={{ duration: 0.6 }}
      className={cn(
        'relative bg-white rounded-lg shadow-lg border-2 border-gray-300',
        'overflow-hidden',
        sizeClasses[size],
        className
      )}
      style={{ aspectRatio: '5/7' }}
    >
      {/* Gradiente sutil de fundo */}
      <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-lg"></div>

      {/* Conteúdo da carta */}
      <div className="relative z-10 flex flex-col justify-between h-full p-1">
        {/* Canto superior esquerdo */}
        <div className="flex flex-col items-start leading-none">
          <span className={cn('font-bold', SUIT_COLORS[card.suit], textSizes[size].rank)}>
            {card.rank}
          </span>
          <span className={cn('leading-none', SUIT_COLORS[card.suit], textSizes[size].suit)}>
            {SUIT_SYMBOLS[card.suit]}
          </span>
        </div>

        {/* Símbolo central */}
        <div className="flex-1 flex items-center justify-center">
          <span className={cn('font-bold', SUIT_COLORS[card.suit], textSizes[size].center)}>
            {SUIT_SYMBOLS[card.suit]}
          </span>
        </div>

        {/* Canto inferior direito (rotacionado) */}
        <div className="flex flex-col items-end leading-none transform rotate-180">
          <span className={cn('font-bold', SUIT_COLORS[card.suit], textSizes[size].rank)}>
            {card.rank}
          </span>
          <span className={cn('leading-none', SUIT_COLORS[card.suit], textSizes[size].suit)}>
            {SUIT_SYMBOLS[card.suit]}
          </span>
        </div>
      </div>
    </motion.div>
  )
}
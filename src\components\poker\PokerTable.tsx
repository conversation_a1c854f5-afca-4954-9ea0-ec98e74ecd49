'use client'

import { PokerTableProps } from '@/types'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import Card from '@/components/ui/Card'
import ActionHistory from '@/components/poker/ActionHistory'
import GameContext from '@/components/poker/GameContext'
import { useLanguage } from '@/hooks/useLanguage'

// Função para explicar por que a ação escolhida foi incorreta
function getIncorrectActionExplanation(playerAction?: string, correctAction?: string, gameContext?: any): string {
  if (!playerAction || !correctAction) return ''

  // Explicações específicas baseadas no contexto
  const contextualExplanations: Record<string, Record<string, string>> = {
    fold: {
      bet: 'Foldar aqui é muito passivo. Com overcards e posição, você deveria apostar para extrair valor e aplicar pressão nos oponentes.',
      call: 'Foldar é muito apertado considerando as pot odds. Você tem equidade suficiente para pagar e ver mais cartas.',
      raise: 'Foldar desperdiça uma oportunidade de construir o pote com uma mão forte. Um raise seria mais lucrativo.',
      check: 'Foldar quando pode fazer check gratuitamente é um erro básico de estratégia.'
    },
    call: {
      bet: 'Apenas pagar é passivo demais nesta situação. Você deveria apostar para tomar controle do pote e extrair valor.',
      fold: 'Pagar com uma mão marginal pode ser custoso. Fold seria mais econômico considerando a ação futura.',
      raise: 'Pagar não extrai valor máximo da sua mão forte. Um raise construiria um pote maior.',
      check: 'Pagar quando pode fazer check gratuitamente desperdiça fichas desnecessariamente.'
    },
    bet: {
      fold: 'Apostar como bluff aqui é arriscado demais. Fold seria mais seguro contra o range do oponente.',
      call: 'Apostar não faz sentido se você só quer ver a próxima carta. Seja mais decisivo na sua estratégia.',
      raise: 'Uma aposta simples pode não aplicar pressão suficiente. Um raise seria mais efetivo com sua mão premium.',
      check: 'Apostar pode ser muito agressivo nesta situação. Check controlaria melhor o tamanho do pote.'
    },
    check: {
      bet: 'Check é muito passivo com sua mão. Você deveria apostar para extrair valor e proteger contra draws.',
      fold: 'Check quando deveria foldar pode te colocar em situações difíceis nas próximas streets.',
      call: 'Check não faz sentido se há uma aposta para pagar. Defina sua estratégia claramente.',
      raise: 'Check desperdiça uma oportunidade de construir um pote grande com uma mão forte.'
    },
    raise: {
      fold: 'Aumentar como bluff é muito caro nesta situação. Fold seria mais prudente.',
      call: 'Aumentar quando só quer pagar é inconsistente. Seja mais claro na sua intenção.',
      bet: 'Um raise pode ser agressivo demais aqui. Uma aposta simples seria mais apropriada.',
      check: 'Aumentar quando pode fazer check gratuitamente pode ser desnecessariamente arriscado.'
    }
  }

  return contextualExplanations[playerAction]?.[correctAction] ||
    `Sua escolha de ${playerAction} não é a mais eficiente nesta situação. A estratégia GTO recomenda ${correctAction} baseado na análise de range e equidade.`
}

export default function PokerTable({
  gameState,
  onAction,
  showRecommendation = false,
  recommendation,
  availableActions,
  onNewSituation,
  onTryAgain
}: PokerTableProps) {
  const { t } = useLanguage()
  const { street, pot, communityCards, playerCards, position, stackSize } = gameState

  // Posições dos jogadores na mesa (baseado exatamente na imagem de referência)
  const seatPositions = [
    { position: 'UTG', x: '50%', y: '5%', label: 'UTG' },
    { position: 'MP', x: '88%', y: '20%', label: 'MP' },
    { position: 'CO', x: '88%', y: '80%', label: 'CO' },
    { position: 'BTN', x: '50%', y: '95%', label: 'BTN' },
    { position: 'SB', x: '12%', y: '80%', label: 'SB' },
    { position: 'BB', x: '12%', y: '20%', label: 'BB' }
  ]

  // Determinar ações disponíveis baseado no cenário ou contexto
  const getAvailableActions = () => {
    // Se temos ações disponíveis do cenário, usar essas
    if (availableActions && availableActions.length > 0) {
      return availableActions.map(action => {
        const actionType = action.action.toLowerCase()
        const colors = {
          fold: 'bg-red-600 hover:bg-red-700',
          call: 'bg-gray-600 hover:bg-gray-700',
          raise: 'bg-green-600 hover:bg-green-700',
          check: 'bg-blue-600 hover:bg-blue-700',
          bet: 'bg-green-600 hover:bg-green-700'
        }

        // Labels traduzidos para cada ação
        const labels = {
          fold: t('actions.fold'),
          call: t('actions.call'),
          raise: t('actions.raise'),
          check: t('actions.check'),
          bet: t('actions.bet')
        }

        return {
          action: actionType,
          label: labels[actionType as keyof typeof labels] || actionType.charAt(0).toUpperCase() + actionType.slice(1),
          color: colors[actionType as keyof typeof colors] || 'bg-gray-600 hover:bg-gray-700',
          isCorrect: action.isCorrect,
          frequency: action.frequency
        }
      })
    }

    // Fallback para lógica genérica se não temos ações do cenário
    const actions = []

    // Sempre disponível
    actions.push({ action: 'fold', label: t('actions.fold'), color: 'bg-red-600 hover:bg-red-700' })

    // Se há aposta para pagar
    const hasActiveBet = gameState.playerActions.some(p =>
      p.isActive && (p.action.type === 'bet' || p.action.type === 'raise')
    )

    if (hasActiveBet) {
      actions.push({ action: 'call', label: t('actions.call'), color: 'bg-gray-600 hover:bg-gray-700' })
      actions.push({ action: 'raise', label: t('actions.raise'), color: 'bg-green-600 hover:bg-green-700' })
    } else {
      // Sem aposta - pode check ou bet
      actions.push({ action: 'check', label: t('actions.check'), color: 'bg-blue-600 hover:bg-blue-700' })
      actions.push({ action: 'bet', label: t('actions.bet'), color: 'bg-green-600 hover:bg-green-700' })
    }

    return actions
  }

  const actionButtons = getAvailableActions()

  return (
    <div className="relative w-full max-w-5xl mx-auto">
      {/* Mesa de poker - formato oval como na imagem */}
      <div className="relative w-full h-[450px] mx-auto">
        {/* Feltro da mesa */}
        <div className="absolute inset-0 bg-gradient-to-br from-green-700 via-green-800 to-green-900 rounded-full border-8 border-amber-700 shadow-2xl overflow-hidden">
          {/* Textura do feltro */}
          <div className="absolute inset-2 bg-gradient-to-br from-green-600 to-green-800 rounded-full opacity-90"></div>
          {/* Borda interna decorativa */}
          <div className="absolute inset-6 border-2 border-green-500/20 rounded-full"></div>
          {/* Rail interno da mesa */}
          <div className="absolute inset-3 border border-amber-600/50 rounded-full"></div>
        </div>

        {/* Área central - cartas comunitárias e pote */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
          {/* Cartas comunitárias */}
          <div className="flex justify-center items-center mb-6">
            {street !== 'preflop' ? (
              <div className="flex space-x-3">
                {communityCards.map((card, index) => (
                  <motion.div
                    key={index}
                    initial={{ scale: 0, rotateY: 180 }}
                    animate={{ scale: 1, rotateY: 0 }}
                    transition={{ delay: index * 0.2, duration: 0.6 }}
                  >
                    <Card card={card} size="medium" />
                  </motion.div>
                ))}

                {/* Cartas futuras (face down) */}
                {street === 'flop' && (
                  <>
                    <div className="w-16 h-20 bg-blue-800 rounded-lg border-2 border-blue-900 flex items-center justify-center overflow-hidden">
                      <div className="text-white text-2xl">🂠</div>
                    </div>
                    <div className="w-16 h-20 bg-blue-800 rounded-lg border-2 border-blue-900 flex items-center justify-center overflow-hidden">
                      <div className="text-white text-2xl">🂠</div>
                    </div>
                  </>
                )}

                {street === 'turn' && (
                  <div className="w-16 h-20 bg-blue-800 rounded-lg border-2 border-blue-900 flex items-center justify-center overflow-hidden">
                    <div className="text-white text-2xl">🂠</div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex space-x-3">
                {[1, 2, 3, 4, 5].map((_, index) => (
                  <div key={index} className="w-16 h-20 bg-blue-800 rounded-lg border-2 border-blue-900 flex items-center justify-center overflow-hidden">
                    <div className="text-white text-2xl">🂠</div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pote central */}
          <div className="text-center">
            <div className="bg-green-900 rounded-full px-4 py-2 border-2 border-yellow-600">
              <div className="text-yellow-400 font-bold text-lg">
                {t('poker.pot')}: ${pot}
              </div>
            </div>
          </div>
        </div>

        {/* Posições dos jogadores */}
        {seatPositions.map((seat) => {
          const playerAction = gameState.playerActions.find(p => p.position === seat.position)
          const isCurrentPlayer = seat.position === position.name

          return (
            <div
              key={seat.position}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 z-20"
              style={{ left: seat.x, top: seat.y }}
            >
              <div className="flex flex-col items-center space-y-2">
                {/* Indicador de posição */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className={cn(
                    'px-3 py-1 rounded-full text-white text-sm font-bold shadow-lg border-2',
                    isCurrentPlayer
                      ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 border-yellow-300 shadow-yellow-400/50 animate-pulse'
                      : 'bg-gradient-to-r from-gray-600 to-gray-700 border-gray-500'
                  )}
                >
                  {seat.label}
                </motion.div>

                {/* Stack do jogador */}
                {(isCurrentPlayer || playerAction) && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="bg-gray-800 rounded-lg px-3 py-1 border border-gray-600 shadow-lg"
                  >
                    <div className="text-green-400 text-sm font-semibold">
                      ${(isCurrentPlayer ? stackSize : playerAction?.stackSize || 1000).toLocaleString()}
                    </div>
                  </motion.div>
                )}

                {/* Ação do jogador */}
                {playerAction && !isCurrentPlayer && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 }}
                    className={cn(
                      'px-2 py-1 rounded-full text-xs font-bold shadow-lg border',
                      playerAction.action.type === 'fold' && 'bg-red-600 border-red-500 text-white',
                      playerAction.action.type === 'call' && 'bg-blue-600 border-blue-500 text-white',
                      playerAction.action.type === 'raise' && 'bg-green-600 border-green-500 text-white',
                      playerAction.action.type === 'check' && 'bg-gray-600 border-gray-500 text-white',
                      playerAction.action.type === 'bet' && 'bg-orange-600 border-orange-500 text-white'
                    )}
                  >
                    {(() => {
                      const actionType = playerAction.action.type;
                      if (actionType === 'pendente' || actionType === 'pending') return 'Pendente';
                      if (actionType === 'fold') return 'Desistir';
                      if (actionType === 'call') return 'Pagar';
                      if (actionType === 'raise') return 'Aumentar';
                      if (actionType === 'check') return 'Check';
                      if (actionType === 'bet') return 'Apostar';
                      return actionType.toUpperCase();
                    })()}

                  </motion.div>
                )}

                {/* Indicador "Sua vez" */}
                {isCurrentPlayer && gameState.actionToUser && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8 }}
                    className="bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold animate-pulse"
                  >
                    {t('poker.yourTurn')}
                  </motion.div>
                )}
              </div>
            </div>
          )
        })}

        {/* Cartas do jogador (posição baseada na posição atual) */}
        {(() => {
          const currentSeat = seatPositions.find(seat => seat.position === position.name)
          if (!currentSeat) return null

          // Calcular posição das cartas baseada na posição do jogador
          let cardX = currentSeat.x
          let cardY = currentSeat.y

          // Ajustar posição das cartas para ficarem mais próximas ao centro da mesa
          switch (currentSeat.position) {
            case 'UTG':
              cardY = '25%'
              break
            case 'MP':
              cardX = '75%'
              cardY = '35%'
              break
            case 'CO':
              cardX = '75%'
              cardY = '65%'
              break
            case 'BTN':
              cardY = '75%'
              break
            case 'SB':
              cardX = '25%'
              cardY = '65%'
              break
            case 'BB':
              cardX = '25%'
              cardY = '35%'
              break
          }

          return (
            <div
              className="absolute transform -translate-x-1/2 -translate-y-1/2 z-30"
              style={{ left: cardX, top: cardY }}
            >
              <div className="flex space-x-1">
                {playerCards.map((card, cardIndex) => (
                  <motion.div
                    key={cardIndex}
                    initial={{ y: 20, opacity: 0, scale: 0.8 }}
                    animate={{ y: 0, opacity: 1, scale: 1 }}
                    transition={{ delay: cardIndex * 0.3 }}
                  >
                    <Card card={card} size="small" />
                  </motion.div>
                ))}
              </div>
            </div>
          )
        })()}
      </div>

      {/* Informações essenciais da mão - Logo após a mesa */}
      <div className="mt-6 bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-gray-400 text-sm">{t('poker.street')}</div>
            <div className="text-white font-bold capitalize">{t(`streets.${street}`)}</div>
          </div>
          <div>
            <div className="text-gray-400 text-sm">{t('poker.position')}</div>
            <div className="text-white font-bold">{position.name}</div>
          </div>
          <div>
            <div className="text-gray-400 text-sm">{t('poker.stack')}</div>
            <div className="text-white font-bold">${stackSize.toLocaleString()}</div>
          </div>
          <div>
            <div className="text-gray-400 text-sm">{t('poker.pot')}</div>
            <div className="text-white font-bold">${pot.toLocaleString()}</div>
          </div>
        </div>
      </div>

      {/* Botões de ação - Logo abaixo das informações essenciais */}
      <div className="mt-6 flex flex-wrap justify-center gap-4">
        {actionButtons.map((button) => (
          <motion.button
            key={button.action}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onAction({ type: button.action as any })}
            className={cn(
              'px-10 py-4 rounded-lg font-semibold text-white shadow-lg transition-all duration-200 text-lg',
              button.color
            )}
          >
            {button.label}
          </motion.button>
        ))}
      </div>

      {/* Feedback GTO - Aparece após a ação e permanece */}
      {showRecommendation && recommendation && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mt-6 rounded-lg p-6 border-2 ${gameState.lastAction?.type === recommendation.action.type
            ? 'bg-green-900/50 border-green-500'
            : 'bg-red-900/50 border-red-500'
            }`}
        >
          {/* Header com resultado */}
          <div className="mb-6">
            {gameState.lastAction?.type === recommendation.action.type ? (
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-2xl">✓</span>
                </div>
                <div>
                  <h3 className="text-green-300 font-bold text-xl">{t('poker.excellentPlay')}</h3>
                  <p className="text-green-200">{t('poker.youChoseGto')}</p>
                </div>
              </div>
            ) : (
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-2xl">✗</span>
                </div>
                <div>
                  <h3 className="text-red-300 font-bold text-xl">{t('poker.actionNotRecommended')}</h3>
                  <p className="text-red-200">
                    {t('poker.youChose')} <span className="font-bold capitalize">{gameState.lastAction?.type}</span>,
                    {t('poker.butGtoRecommends')} <span className="font-bold capitalize text-green-400">{recommendation.action.type}</span>
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Comparação de ações */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Sua escolha */}
            <div className={`rounded-lg p-4 border-2 ${gameState.lastAction?.type === recommendation.action.type
              ? 'bg-green-800/30 border-green-500'
              : 'bg-red-800/30 border-red-500'
              }`}>
              <h4 className={`font-bold mb-2 ${gameState.lastAction?.type === recommendation.action.type
                ? 'text-green-300'
                : 'text-red-300'
                }`}>
                {t('poker.yourChoice')}
              </h4>
              <div className="text-white font-bold text-lg capitalize">
                {gameState.lastAction?.type}
                {gameState.lastAction?.amount && (
                  <span className="text-gray-300"> ${gameState.lastAction.amount}</span>
                )}
              </div>
            </div>

            {/* Ação GTO */}
            <div className="bg-green-800/30 border-2 border-green-500 rounded-lg p-4">
              <h4 className="text-green-300 font-bold mb-2">{t('poker.gtoRecommended')}</h4>
              <div className="text-white font-bold text-lg capitalize">
                {recommendation.action.type}
                {recommendation.action.amount && (
                  <span className="text-green-400"> ${recommendation.action.amount}</span>
                )}
              </div>
              <div className="text-green-200 text-sm mt-1">
                {t('poker.frequency')}: {(recommendation.frequency * 100).toFixed(1)}% |
                {t('poker.equity')}: {(recommendation.equity * 100).toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Explicação detalhada */}
          <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
            <h4 className="text-blue-300 font-semibold mb-3 flex items-center">
              <span className="mr-2">💡</span>
              {gameState.lastAction?.type === recommendation.action.type
                ? t('poker.whyCorrectPlay')
                : t('poker.analysisYourDecision')
              }
            </h4>
            <p className="text-gray-300 leading-relaxed mb-3">
              {recommendation.explanation}
            </p>

            {/* Explicação adicional para jogadas incorretas */}
            {gameState.lastAction?.type !== recommendation.action.type && (
              <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-500/30 rounded-lg">
                <h5 className="text-yellow-300 font-semibold mb-2">{t('poker.analysisYourChoice')}</h5>
                <p className="text-yellow-200 text-sm mb-3">
                  {getIncorrectActionExplanation(gameState.lastAction?.type, recommendation.action.type, gameState)}
                </p>

                {/* Métricas comparativas */}
                <div className="grid grid-cols-2 gap-3 mt-3">
                  <div className="bg-red-900/30 border border-red-500/30 rounded p-2">
                    <div className="text-red-300 text-xs font-semibold">{t('poker.yourAction')}</div>
                    <div className="text-red-200 text-sm capitalize">{gameState.lastAction?.type}</div>
                    <div className="text-red-400 text-xs">{t('poker.gtoFrequency')}: ~{Math.max(5, Math.round((1 - recommendation.frequency) * 100))}%</div>
                  </div>
                  <div className="bg-green-900/30 border border-green-500/30 rounded p-2">
                    <div className="text-green-300 text-xs font-semibold">{t('poker.recommendedAction')}</div>
                    <div className="text-green-200 text-sm capitalize">{recommendation.action.type}</div>
                    <div className="text-green-400 text-xs">{t('poker.gtoFrequency')}: {Math.round(recommendation.frequency * 100)}%</div>
                  </div>
                </div>
              </div>
            )}

            {/* Métricas adicionais para ações corretas */}
            {gameState.lastAction?.type === recommendation.action.type && (
              <div className="mt-4 p-3 bg-green-900/30 border border-green-500/30 rounded-lg">
                <h5 className="text-green-300 font-semibold mb-2">{t('poker.excellentDecision')}</h5>
                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center">
                    <div className="text-green-400 font-bold text-lg">{Math.round(recommendation.frequency * 100)}%</div>
                    <div className="text-green-300 text-xs">{t('poker.gtoFrequency')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-green-400 font-bold text-lg">{Math.round(recommendation.equity * 100)}%</div>
                    <div className="text-green-300 text-xs">{t('poker.equity')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-green-400 font-bold text-lg">+10</div>
                    <div className="text-green-300 text-xs">{t('poker.xpGained')}</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Botões de navegação */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onNewSituation}
              className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
            >
              <span className="mr-2">🔄</span>
              {t('poker.newSituation')}
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onTryAgain}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
            >
              <span className="mr-2">🎯</span>
              {t('poker.tryAgain')}
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Seção informativa - Para quem quer se aprofundar */}
      <div className="mt-12 space-y-6">
        {/* Contexto da Situação */}
        <GameContext gameState={gameState} />

        {/* Histórico de Ações */}
        {gameState.playerActions && gameState.playerActions.length > 0 && (
          <ActionHistory
            playerActions={gameState.playerActions}
            currentPosition={position.name}
          />
        )}
      </div>
    </div>
  )
}
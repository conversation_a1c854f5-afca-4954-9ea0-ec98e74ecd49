'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  Play, 
  Target, 
  Zap, 
  Clock, 
  BarChart3,
  Filter,
  Search,
  BookOpen
} from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'
import { User } from '@/types'
// import Navbar from '@/components/dashboard/Navbar' // Removido - componente não existe
import { getRandomScenarios } from '@/lib/scenario-service'

export default function TrainingPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [categories, setCategories] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Criar usuário mock para teste
    const mockUser = {
      id: 'mock-user',
      username: 'test-user',
      language: 'pt'
    }
    
    setUser(mockUser)
    loadCategories()
  }, [router])

  const loadCategories = async () => {
    try {
      setIsLoading(true)
      // Por enquanto, usar categorias fixas até implementar a função
      const availableCategories = ['cbet_spots', '3bet_defense', 'bluff_catching', 'value_betting']
      setCategories(availableCategories)
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const trainingModes = [
    {
      title: t('training.quiz'),
      description: t('dashboard.quickTest'),
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      href: '/dashboard/training/quiz',
      duration: '5-10 min'
    },
    {
      title: t('training.simulation'),
      description: t('dashboard.fullSimulation'),
      icon: Play,
      color: 'from-green-500 to-green-600',
      href: '/dashboard/training/simulation',
      duration: '15-30 min'
    },
    {
      title: t('training.practice'),
      description: t('dashboard.freePractice'),
      icon: Zap,
      color: 'from-purple-500 to-purple-600',
      href: '/dashboard/training/practice',
      duration: t('dashboard.unlimited')
    }
  ]

  const allTrainingCategories = [
    {
      id: 'preflop',
      title: 'Preflop',
      description: 'Aprenda decisões fundamentais pré-flop: aberturas, 3-bets, calls e folds',
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      count: 67,
      difficulty: 'beginner',
      tags: ['preflop', 'abertura', 'fold', 'call']
    },
    {
      id: '3bet',
      title: '3-bet',
      description: 'Estratégias para 3-bet: valor, blefe, defesa e 4-bet spots',
      icon: Play,
      color: 'from-red-500 to-red-600',
      count: 29,
      difficulty: 'intermediate',
      tags: ['3bet', 'blefe', 'valor', 'defesa', '4bet']
    },
    {
      id: 'cbet',
      title: 'C-bet',
      description: 'Aprenda quando e como fazer continuation bets efetivos',
      icon: BookOpen,
      color: 'from-green-500 to-green-600',
      count: 29,
      difficulty: 'beginner',
      tags: ['cbet', 'continuation', 'flop', 'bet']
    },
    {
      id: 'turn_aggression',
      title: 'Turn Aggression',
      description: 'Maximize valor e aplique pressão no turn com jogadas agressivas',
      icon: Zap,
      color: 'from-orange-500 to-orange-600',
      count: 41,
      difficulty: 'advanced',
      tags: ['turn', 'agressão', 'valor', 'pressão']
    },
    {
      id: 'river_value',
      title: 'River Value',
      description: 'Extraia valor máximo no river com bets e raises precisos',
      icon: BarChart3,
      color: 'from-purple-500 to-purple-600',
      count: 24,
      difficulty: 'intermediate',
      tags: ['river', 'valor', 'bet', 'raise']
    },
    {
      id: 'river_bluff',
      title: 'River Bluff',
      description: 'Identifique e capture bluffs do oponente no river',
      icon: Target,
      color: 'from-yellow-500 to-yellow-600',
      count: 18,
      difficulty: 'advanced',
      tags: ['river', 'bluff', 'catch', 'call']
    },
    {
      id: 'donk_lead',
      title: 'Donk Lead',
      description: 'Quando e como usar donk leads para proteção e valor',
      icon: Play,
      color: 'from-teal-500 to-teal-600',
      count: 22,
      difficulty: 'intermediate',
      tags: ['donk', 'lead', 'proteção', 'valor']
    },
    {
      id: 'float',
      title: 'Float',
      description: 'Estratégias de float para explorar weakness e aplicar pressão',
      icon: BookOpen,
      color: 'from-indigo-500 to-indigo-600',
      count: 18,
      difficulty: 'advanced',
      tags: ['float', 'weakness', 'pressão', 'call']
    }
  ]

  // Filtrar categorias baseado na pesquisa e filtros
  const filteredCategories = allTrainingCategories.filter(category => {
    // Filtro por categoria selecionada
    if (selectedCategory !== 'all' && category.id !== selectedCategory) {
      return false
    }
    
    // Filtro por dificuldade selecionada
    if (selectedDifficulty !== 'all' && category.difficulty !== selectedDifficulty) {
      return false
    }
    
    // Filtro por termo de pesquisa
    if (searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase()
      const matchesTitle = category.title.toLowerCase().includes(searchLower)
      const matchesDescription = category.description.toLowerCase().includes(searchLower)
      const matchesTags = category.tags.some(tag => tag.toLowerCase().includes(searchLower))
      
      if (!matchesTitle && !matchesDescription && !matchesTags) {
        return false
      }
    }
    
    return true
  })

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            {t('training.trainingCenter')}
          </h1>
          <p className="text-gray-400">
            {t('training.chooseTrainingMode')}
          </p>
          {/* Debug info - remover depois */}
          <div className="text-xs text-gray-500 mt-2">
            Debug: Current language = {JSON.stringify({ language: useLanguage().language })}
          </div>
        </div>

        {/* Modos de Treinamento */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <h2 className="text-xl font-bold text-white mb-6">{t('training.trainingModes')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {trainingModes.map((mode, index) => (
              <motion.div
                key={mode.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link href={mode.href}>
                  <div className={`bg-gradient-to-br ${mode.color} rounded-xl p-6 hover:scale-105 transition-transform cursor-pointer`}>
                    <div className="flex items-center justify-between mb-4">
                      <mode.icon className="w-8 h-8 text-white" />
                      <div className="flex items-center text-white/80 text-sm">
                        <Clock className="w-4 h-4 mr-1" />
                        {mode.duration}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">{mode.title}</h3>
                    <p className="text-white/80 text-sm">{mode.description}</p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Filtros */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-xl p-6 mb-8 border border-gray-700"
        >
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todas as Categorias</option>
                {allTrainingCategories.map(category => (
                  <option key={category.id} value={category.id}>{category.title}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t('training.allDifficulties')}</option>
                <option value="beginner">{t('training.beginner')}</option>
                <option value="intermediate">{t('training.intermediate')}</option>
                <option value="advanced">{t('training.advanced')}</option>
              </select>
            </div>

            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('training.searchCategory')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </motion.div>

        {/* Categorias de Treinamento */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h2 className="text-xl font-bold text-white mb-6">{t('training.trainingCategories')}</h2>
          {filteredCategories.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Nenhuma categoria encontrada</h3>
                <p className="text-sm">
                  {searchTerm ? `Nenhum resultado para "${searchTerm}"` : 'Tente ajustar os filtros'}
                </p>
              </div>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                  setSelectedDifficulty('all')
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Limpar Filtros
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
                className="bg-gray-800 rounded-xl border border-gray-700 hover:border-gray-600 transition-colors cursor-pointer group"
                onClick={() => router.push(`/dashboard/training/practice?category=${category.id}`)}
              >
                <div className="p-6">
                  <div className={`w-12 h-12 bg-gradient-to-br ${category.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                    <category.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">{category.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{category.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400 text-sm font-medium">
                      {category.count} cenários
                    </span>
                    <div className="flex space-x-2">
                      <Link 
                        href={`/dashboard/training/practice?category=${category.id}`}
                        className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors"
                        onClick={(e) => e.stopPropagation()}
                      >
                        Praticar
                      </Link>
                      <Link 
                        href={`/dashboard/training/simulation?category=${category.id}`}
                        className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded transition-colors"
                        onClick={(e) => e.stopPropagation()}
                      >
                        Simular
                      </Link>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
            </div>
          )}
        </motion.div>

        {/* Estatísticas Rápidas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 border border-blue-500/30"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-bold text-white mb-2">{t('training.yourProgress')}</h3>
              <p className="text-blue-200">
                {t('training.continueTrainingImprove')}
              </p>
            </div>
            <Link 
              href="/dashboard/statistics"
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {t('training.viewStatistics')}
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
'use client'

import { motion } from 'framer-motion'
import { Achievement } from '@/types'
import { useLanguage } from '@/hooks/useLanguage'
import { Lock, Star, Trophy, Zap } from 'lucide-react'

interface AchievementCardProps {
  achievement: Achievement
  isUnlocked: boolean
  progress?: number
  className?: string
}

export default function AchievementCard({ 
  achievement, 
  isUnlocked, 
  progress = 0, 
  className 
}: AchievementCardProps) {
  const { language, t } = useLanguage()
  
  const name = language === 'pt' ? achievement.namePt : achievement.nameEn
  const description = language === 'pt' ? achievement.descriptionPt : achievement.descriptionEn

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'training':
        return <Zap className="w-5 h-5" />
      case 'accuracy':
        return <Star className="w-5 h-5" />
      case 'streak':
        return <Trophy className="w-5 h-5" />
      case 'level':
        return <Star className="w-5 h-5" />
      default:
        return <Trophy className="w-5 h-5" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'training':
        return 'text-blue-400'
      case 'accuracy':
        return 'text-green-400'
      case 'streak':
        return 'text-yellow-400'
      case 'level':
        return 'text-purple-400'
      default:
        return 'text-gray-400'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
        isUnlocked 
          ? 'bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/50 shadow-lg shadow-yellow-500/20' 
          : 'bg-gray-800/50 border-gray-700 hover:border-gray-600'
      } ${className}`}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
      </div>

      <div className="relative p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              isUnlocked 
                ? 'bg-yellow-500/20 text-yellow-400' 
                : 'bg-gray-700 text-gray-400'
            }`}>
              {getCategoryIcon(achievement.category)}
            </div>
            
            <div>
              <h3 className={`font-bold text-lg ${
                isUnlocked ? 'text-yellow-300' : 'text-gray-300'
              }`}>
                {name}
              </h3>
              <div className="flex items-center space-x-2">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  isUnlocked 
                    ? 'bg-yellow-500/20 text-yellow-300' 
                    : 'bg-gray-700 text-gray-400'
                }`}>
                  {t(`achievements.categories.${achievement.category}` as any)}
                </span>
                <span className={`text-sm font-medium ${
                  isUnlocked ? 'text-green-400' : 'text-gray-500'
                }`}>
                  +{achievement.xpReward} XP
                </span>
              </div>
            </div>
          </div>

          {/* Status icon */}
          <div className={`p-2 rounded-full ${
            isUnlocked 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-gray-700 text-gray-500'
          }`}>
            {isUnlocked ? (
              <Trophy className="w-5 h-5" />
            ) : (
              <Lock className="w-5 h-5" />
            )}
          </div>
        </div>

        {/* Description */}
        <p className={`text-sm mb-4 ${
          isUnlocked ? 'text-gray-200' : 'text-gray-400'
        }`}>
          {description}
        </p>

        {/* Progress bar (for locked achievements) */}
        {!isUnlocked && progress > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">{t('achievements.progress')}</span>
              <span className="text-gray-400">{Math.round(progress * 100)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progress * 100}%` }}
                transition={{ duration: 1, delay: 0.5 }}
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
              />
            </div>
          </div>
        )}

        {/* Unlocked indicator */}
        {isUnlocked && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center space-x-2 bg-green-900/30 border border-green-500/30 rounded-lg py-2 px-4"
          >
            <Trophy className="w-4 h-4 text-green-400" />
            <span className="text-green-300 text-sm font-medium">{t('achievements.unlocked')}</span>
          </motion.div>
        )}

        {/* Glow effect for unlocked achievements */}
        {isUnlocked && (
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500/10 to-orange-500/10 pointer-events-none" />
        )}
      </div>
    </motion.div>
  )
}
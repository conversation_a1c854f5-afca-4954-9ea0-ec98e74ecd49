const { PrismaClient } = require('@prisma/client');

async function debugScenario() {
  const prisma = new PrismaClient();
  
  try {
    // Buscar o cenário problemático
    const scenario = await prisma.gTOScenario.findUnique({
      where: { id: 'cmdi3k4t700ctwqw3kgcrwccw' }
    });
    
    if (scenario) {
      console.log('=== CENÁRIO BRUTO DO BANCO ===');
      console.log('ID:', scenario.id);
      console.log('Nome:', scenario.name);
      console.log('Street:', scenario.street);
      console.log('Player Cards (raw):', scenario.playerCards);
      console.log('Community Cards (raw):', scenario.communityCards);
      
      console.log('\n=== DADOS PARSEADOS ===');
      const playerCards = JSON.parse(scenario.playerCards);
      const communityCards = JSON.parse(scenario.communityCards);
      
      console.log('Player Cards (parsed):', playerCards);
      console.log('Community Cards (parsed):', communityCards);
      
      console.log('\n=== TESTE DE CONVERSÃO ===');
      
      // Testar conversão das cartas do jogador
      console.log('Testando card1:', playerCards.card1);
      console.log('Testando card2:', playerCards.card2);
      
      // Testar conversão das cartas comunitárias
      if (communityCards.flop) {
        console.log('Flop:', communityCards.flop);
      }
      if (communityCards.turn) {
        console.log('Turn:', communityCards.turn);
      }
      if (communityCards.river) {
        console.log('River:', communityCards.river);
      }
    } else {
      console.log('Cenário não encontrado');
    }
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugScenario();
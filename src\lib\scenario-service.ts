import { prisma } from './prisma'
import { GameState, PokerAction } from '@/types'

export interface GTOScenarioFromDB {
  id: string
  name: string
  nameEn: string
  namePt: string
  description: string
  descriptionEn: string
  descriptionPt: string
  gameType: string
  street: string
  position: string
  playerCards: string
  communityCards: string
  potSize: number
  playerStack: number
  opponentActions: string
  availableActions: string
  correctAction: string
  equity: number
  frequency: number
  explanation: string
  explanationEn: string
  explanationPt: string
  context: string
  difficulty: string
  category: string
  tags: string
}

export interface ProcessedScenario {
  id: string
  name: string
  description: string
  gameState: GameState
  recommendation: {
    action: PokerAction
    frequency: number
    equity: number
    explanation: string
  }
  availableActions: Array<{
    action: string
    amount?: number
    isCorrect: boolean
    frequency: number
    explanation: string
  }>
}

export interface ScenarioFilters {
  street?: 'preflop' | 'flop' | 'turn' | 'river'
  position?: 'UTG' | 'MP' | 'CO' | 'BTN' | 'SB' | 'BB'
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  category?: string
  gameType?: 'cash' | 'tournament'
  tags?: string[]
}

export async function getRandomScenarios(count: number = 5): Promise<ProcessedScenario[]> {
  try {
    // Buscar todos os IDs disponíveis
    const allScenarios = await prisma.gTOScenario.findMany({
      select: { id: true }
    })
    
    if (allScenarios.length === 0) {
      console.log('Nenhum cenário encontrado no banco de dados')
      return []
    }
    
    // Embaralhar os IDs e pegar os primeiros 'count'
    const shuffledIds = allScenarios
      .map(s => s.id)
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.min(count, allScenarios.length))
    
    // Buscar os cenários pelos IDs selecionados
    const scenarios = await prisma.gTOScenario.findMany({
      where: {
        id: {
          in: shuffledIds
        }
      }
    })

    console.log(`Encontrados ${scenarios.length} cenários aleatórios de ${allScenarios.length} total`)
    return scenarios.map(processScenario)
  } catch (error) {
    console.error('Erro ao buscar cenários:', error)
    return []
  }
}

export async function getFilteredScenarios(filters: ScenarioFilters, count: number = 10): Promise<ProcessedScenario[]> {
  try {
    const whereClause: any = {}
    
    // Aplicar filtros básicos
    if (filters.street) {
      whereClause.street = filters.street
    }
    
    if (filters.position) {
      whereClause.position = filters.position
    }
    
    if (filters.difficulty) {
      whereClause.difficulty = filters.difficulty
    }
    
    if (filters.gameType) {
      whereClause.gameType = filters.gameType
    }

    // Filtro inteligente para categoria - busca em múltiplos campos
    if (filters.category) {
      const categoryTerm = filters.category.toLowerCase()
      
      whereClause.OR = [
        // Busca exata na categoria
        { category: { equals: filters.category } },
        
        // Busca parcial na categoria (case insensitive)
        { category: { contains: categoryTerm } },
        
        // Busca nas tags (JSON)
        { tags: { contains: categoryTerm } },
        
        // Busca no nome do filtro personalizado
        { 
          customFilter: {
            name: { contains: categoryTerm }
          }
        },
        
        // Busca no nome do cenário
        { name: { contains: categoryTerm } },
        
        // Busca na descrição
        { description: { contains: categoryTerm } }
      ]
    }
    
    // Para tags específicas, adicionar busca adicional
    if (filters.tags && filters.tags.length > 0) {
      const tagConditions = filters.tags.map(tag => ({
        tags: { contains: tag.toLowerCase() }
      }))
      
      if (whereClause.OR) {
        whereClause.OR.push(...tagConditions)
      } else {
        whereClause.OR = tagConditions
      }
    }

    // Buscar todos os cenários que atendem aos filtros
    const allFilteredScenarios = await prisma.gTOScenario.findMany({
      where: whereClause,
      select: { id: true }
    })

    if (allFilteredScenarios.length === 0) {
      console.log('Nenhum cenário encontrado com os filtros aplicados:', filters)
      
      // Fallback: buscar cenários similares sem o filtro de categoria
      if (filters.category) {
        console.log('Tentando busca mais ampla...')
        const fallbackWhere = { ...whereClause }
        delete fallbackWhere.OR
        
        const fallbackScenarios = await prisma.gTOScenario.findMany({
          where: fallbackWhere,
          select: { id: true },
          take: count * 2
        })
        
        if (fallbackScenarios.length > 0) {
          console.log(`Encontrados ${fallbackScenarios.length} cenários no fallback`)
          const shuffledIds = fallbackScenarios
            .map(s => s.id)
            .sort(() => Math.random() - 0.5)
            .slice(0, Math.min(count, fallbackScenarios.length))

          const scenarios = await prisma.gTOScenario.findMany({
            where: { id: { in: shuffledIds } },
            include: { customFilter: true }
          })

          return scenarios.map(processScenario)
        }
      }
      
      return []
    }

    // Embaralhar os IDs e pegar os primeiros 'count'
    const shuffledIds = allFilteredScenarios
      .map(s => s.id)
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.min(count, allFilteredScenarios.length))

    // Buscar os cenários pelos IDs selecionados
    const scenarios = await prisma.gTOScenario.findMany({
      where: {
        id: {
          in: shuffledIds
        }
      },
      include: {
        customFilter: true
      }
    })

    console.log(`Encontrados ${scenarios.length} cenários filtrados de ${allFilteredScenarios.length} que atendem aos filtros:`, filters)
    return scenarios.map(processScenario)
  } catch (error) {
    console.error('Erro ao buscar cenários filtrados:', error)
    return []
  }
}

export async function getScenariosByCategory(category: string, count: number = 10): Promise<ProcessedScenario[]> {
  return getFilteredScenarios({ category }, count)
}

export async function getScenariosByPosition(position: ScenarioFilters['position'], count: number = 10): Promise<ProcessedScenario[]> {
  return getFilteredScenarios({ position }, count)
}

export async function getScenariosByStreet(street: ScenarioFilters['street'], count: number = 10): Promise<ProcessedScenario[]> {
  return getFilteredScenarios({ street }, count)
}

export async function getScenariosByDifficulty(difficulty: ScenarioFilters['difficulty'], count: number = 10): Promise<ProcessedScenario[]> {
  return getFilteredScenarios({ difficulty }, count)
}

export async function getScenarioById(id: string): Promise<ProcessedScenario | null> {
  try {
    const scenario = await prisma.gTOScenario.findUnique({
      where: { id }
    })

    if (!scenario) return null
    return processScenario(scenario)
  } catch (error) {
    console.error('Erro ao buscar cenário:', error)
    return null
  }
}

function processScenario(dbScenario: GTOScenarioFromDB): ProcessedScenario {
  // Parse JSON fields
  const playerCards = JSON.parse(dbScenario.playerCards)
  const communityCards = JSON.parse(dbScenario.communityCards)
  const opponentActions = JSON.parse(dbScenario.opponentActions)
  const availableActions = JSON.parse(dbScenario.availableActions)
  const context = JSON.parse(dbScenario.context)

  // Convert card strings to Card objects
  const convertCard = (cardStr: string) => {
    if (!cardStr || cardStr.length < 2) {
      console.warn('Carta inválida ou vazia:', cardStr)
      return null
    }
    
    const rank = cardStr[0].toUpperCase()
    
    // Suporte tanto para símbolos Unicode quanto para letras
    let suitChar = cardStr.slice(1).toLowerCase()
    
    const suitMap: Record<string, any> = {
      // Letras tradicionais
      'h': 'hearts',
      'd': 'diamonds', 
      'c': 'clubs',
      's': 'spades',
      // Símbolos Unicode
      '♥': 'hearts',
      '♦': 'diamonds',
      '♣': 'clubs', 
      '♠': 'spades'
    }

    const valueMap: Record<string, number> = {
      'A': 14, 'K': 13, 'Q': 12, 'J': 11, 'T': 10,
      '9': 9, '8': 8, '7': 7, '6': 6, '5': 5, '4': 4, '3': 3, '2': 2
    }

    const suit = suitMap[suitChar]
    const value = valueMap[rank]

    if (!suit || !value) {
      console.warn('Carta com formato inválido:', cardStr, { rank, suitChar, suit, value })
      return null
    }

    console.log(`Convertendo carta: ${cardStr} -> ${rank} de ${suit}`)

    return {
      suit,
      rank,
      value
    }
  }

  // Build community cards array - ONLY if not preflop
  const communityCardsArray = []
  if (dbScenario.street !== 'preflop') {
    if (communityCards.flop && Array.isArray(communityCards.flop)) {
      const flopCards = communityCards.flop.map(convertCard).filter(card => card !== null)
      communityCardsArray.push(...flopCards)
    }
    if (communityCards.turn) {
      const turnCard = convertCard(communityCards.turn)
      if (turnCard) communityCardsArray.push(turnCard)
    }
    if (communityCards.river) {
      const riverCard = convertCard(communityCards.river)
      if (riverCard) communityCardsArray.push(riverCard)
    }
  }
  
  console.log('Community cards processadas:', communityCardsArray)

  // Build player cards
  const playerCard1 = convertCard(playerCards.card1)
  const playerCard2 = convertCard(playerCards.card2)
  
  console.log('Player cards raw:', { card1: playerCards.card1, card2: playerCards.card2 })
  console.log('Player cards processadas:', { playerCard1, playerCard2 })
  
  // Filtrar cartas nulas das cartas do jogador
  const validPlayerCards = [playerCard1, playerCard2].filter(card => card !== null)
  
  if (validPlayerCards.length < 2) {
    console.error('Erro: Cartas do jogador inválidas no cenário:', dbScenario.id, dbScenario.name)
  }

  // Build game state
  const gameState: GameState = {
    street: dbScenario.street as any,
    pot: dbScenario.potSize,
    communityCards: communityCardsArray,
    playerCards: validPlayerCards,
    position: {
      name: dbScenario.position as any,
      displayName: dbScenario.position,
      order: getPositionOrder(dbScenario.position)
    },
    stackSize: dbScenario.playerStack,
    gameType: dbScenario.gameType as any,
    playerActions: opponentActions.filter((action: any) => action && action.action).map((action: any) => ({
      position: action.position || 'Unknown',
      action: { type: action.action, amount: action.amount },
      amount: action.amount || 0,
      isActive: action.isActive || false,
      stackSize: action.stackSize
    })),
    actionToUser: true
  }

  // Find correct action
  const correctActionData = availableActions.find((a: any) => a.isCorrect) || availableActions[0] || {
    action: 'fold',
    amount: 0,
    frequency: 1.0,
    explanation: 'Ação padrão'
  }

  const recommendation = {
    action: {
      type: correctActionData.action,
      amount: correctActionData.amount || 0,
      sizing: correctActionData.sizing || 'medium'
    } as PokerAction,
    frequency: correctActionData.frequency || 1.0,
    equity: dbScenario.equity,
    explanation: correctActionData.explanation || 'Sem explicação disponível'
  }

  return {
    id: dbScenario.id,
    name: dbScenario.namePt,
    description: dbScenario.descriptionPt,
    gameState,
    recommendation,
    availableActions
  }
}

function getPositionOrder(position: string): number {
  const orderMap: Record<string, number> = {
    'UTG': 1,
    'MP': 2,
    'CO': 3,
    'BTN': 4,
    'SB': 5,
    'BB': 6
  }
  return orderMap[position] || 1
}
const { PrismaClient } = require('@prisma/client');

async function checkCategories() {
  const prisma = new PrismaClient();
  
  try {
    // <PERSON>car todas as categorias únicas
    const categories = await prisma.gTOScenario.groupBy({
      by: ['category'],
      _count: {
        category: true
      },
      orderBy: {
        _count: {
          category: 'desc'
        }
      }
    });
    
    console.log('=== CATEGORIAS DISPONÍVEIS NO BANCO ===');
    categories.forEach(cat => {
      console.log(`${cat.category}: ${cat._count.category} cenários`);
    });
    
    console.log('\n=== PROCURANDO CENÁRIOS COM "3BET" NO NOME ===');
    const threeBetScenarios = await prisma.gTOScenario.findMany({
      where: {
        OR: [
          { name: { contains: '3bet' } },
          { name: { contains: '3-bet' } },
          { description: { contains: '3bet' } },
          { description: { contains: '3-bet' } },
          { category: { contains: '3bet' } }
        ]
      },
      select: {
        id: true,
        name: true,
        category: true,
        description: true
      },
      take: 10
    });
    
    console.log(`Encontrados ${threeBetScenarios.length} cenários com "3bet" no nome/descrição:`);
    threeBetScenarios.forEach(scenario => {
      console.log(`- ${scenario.name} (categoria: ${scenario.category})`);
    });
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCategories();
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const street = searchParams.get('street')
    const difficulty = searchParams.get('difficulty')
    const category = searchParams.get('category')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (street && street !== 'all') {
      where.street = street
    }
    
    if (difficulty && difficulty !== 'all') {
      where.difficulty = difficulty
    }
    
    if (category && category !== 'all') {
      where.category = category
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { category: { contains: search } }
      ]
    }

    const [scenarios, total] = await Promise.all([
      prisma.gTOScenario.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          description: true,
          gameType: true,
          street: true,
          position: true,
          difficulty: true,
          category: true,
          createdAt: true
        }
      }),
      prisma.gTOScenario.count({ where })
    ])

    return NextResponse.json({
      success: true,
      scenarios,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erro ao buscar cenários:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
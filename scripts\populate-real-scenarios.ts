const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const realScenarios = [
  {
    name: "C-bet no Flop Seco com AK",
    nameEn: "C-bet on Dry Flop with AK",
    namePt: "C-bet no Flop Seco com AK",
    description: "Você abriu UTG com AK e o BB chamou. Flop: Q♠7♣2♦. BB checou. Como proceder?",
    descriptionEn: "You opened UTG with AK and BB called. Flop: Q♠7♣2♦. BB checked. How to proceed?",
    descriptionPt: "Você abriu UTG com AK e o BB chamou. Flop: Q♠7♣2♦. BB checou. Como proceder?",
    gameType: "cash",
    street: "flop",
    position: "UTG",
    stackDepth: "deep",
    playerCards: JSON.stringify({"card1": "As", "card2": "Kh"}),
    communityCards: JSON.stringify({"flop": ["Qs", "7c", "2d"], "turn": null, "river": null}),
    potSize: 15,
    playerStack: 195,
    opponentActions: JSON.stringify([
      {"position": "BB", "action": "check", "isActive": true, "stackSize": 195}
    ]),
    availableActions: JSON.stringify([
      {"action": "check", "frequency": 0.2, "isCorrect": false, "explanation": "Check desperdiça value com top pair"},
      {"action": "bet", "amount": 10, "frequency": 0.8, "isCorrect": true, "explanation": "C-bet para value com top pair é correto"}
    ]),
    correctAction: "bet",
    equity: 0.72,
    frequency: 0.8,
    explanation: "Com top pair no flop seco, c-bet para value é a jogada padrão",
    explanationEn: "With top pair on dry flop, c-bet for value is standard play",
    explanationPt: "Com top pair no flop seco, c-bet para value é a jogada padrão",
    context: JSON.stringify({"situation": "cbet_toppair"}),
    difficulty: "beginner",
    category: "cbet",
    tags: JSON.stringify(["value_bet", "top_pair"])
  },

  {
    name: "3-bet Bluff com A5s",
    nameEn: "3-bet Bluff with A5s", 
    namePt: "3-bet Bluff com A5s",
    description: "CO abriu para 6bb. Você está no BTN com A♠5♠. Fazer 3-bet?",
    descriptionEn: "CO opened to 6bb. You're on BTN with A♠5♠. Make a 3-bet?",
    descriptionPt: "CO abriu para 6bb. Você está no BTN com A♠5♠. Fazer 3-bet?",
    gameType: "cash",
    street: "preflop",
    position: "BTN",
    stackDepth: "deep",
    playerCards: JSON.stringify({"card1": "As", "card2": "5s"}),
    communityCards: JSON.stringify({"flop": null, "turn": null, "river": null}),
    potSize: 9,
    playerStack: 194,
    opponentActions: JSON.stringify([
      {"position": "CO", "action": "raise", "amount": 6, "isActive": true, "stackSize": 194}
    ]),
    availableActions: JSON.stringify([
      {"action": "fold", "frequency": 0.3, "isCorrect": false, "explanation": "A5s tem boa playability para 3-bet"},
      {"action": "call", "frequency": 0.2, "isCorrect": false, "explanation": "Call é passivo demais com esta mão"},
      {"action": "raise", "amount": 18, "frequency": 0.5, "isCorrect": true, "explanation": "3-bet bluff com A5s é uma jogada padrão"}
    ]),
    correctAction: "raise",
    equity: 0.42,
    frequency: 0.5,
    explanation: "A5s é uma excelente mão para 3-bet bluff: tem blockers e boa playability",
    explanationEn: "A5s is excellent for 3-bet bluff: has blockers and good playability",
    explanationPt: "A5s é uma excelente mão para 3-bet bluff: tem blockers e boa playability",
    context: JSON.stringify({"situation": "3bet_bluff"}),
    difficulty: "intermediate",
    category: "preflop",
    tags: JSON.stringify(["3bet", "bluff", "suited_ace"])
  },

  {
    name: "Call no River com Second Pair",
    nameEn: "River Call with Second Pair",
    namePt: "Call no River com Second Pair",
    description: "Você tem J♠T♠ no board A♣J♦5♥8♠2♣. Oponente aposta 80 no river. Pagar?",
    descriptionEn: "You have J♠T♠ on A♣J♦5♥8♠2♣ board. Opponent bets 80 on river. Call?",
    descriptionPt: "Você tem J♠T♠ no board A♣J♦5♥8♠2♣. Oponente aposta 80 no river. Pagar?",
    gameType: "cash",
    street: "river",
    position: "BTN",
    stackDepth: "medium",
    playerCards: JSON.stringify({"card1": "Js", "card2": "Ts"}),
    communityCards: JSON.stringify({"flop": ["Ac", "Jd", "5h"], "turn": "8s", "river": "2c"}),
    potSize: 120,
    playerStack: 85,
    opponentActions: JSON.stringify([
      {"position": "BB", "action": "bet", "amount": 80, "isActive": true, "stackSize": 90}
    ]),
    availableActions: JSON.stringify([
      {"action": "fold", "frequency": 0.4, "isCorrect": false, "explanation": "Second pair pode ser boa o suficiente vs bluffs"},
      {"action": "call", "amount": 80, "frequency": 0.6, "isCorrect": true, "explanation": "Com pot odds favoráveis, call é correto"}
    ]),
    correctAction: "call",
    equity: 0.45,
    frequency: 0.6,
    explanation: "Second pair com pot odds de 2.5:1 é call vs range balanceado do oponente",
    explanationEn: "Second pair with 2.5:1 pot odds is a call vs opponent's balanced range",
    explanationPt: "Second pair com pot odds de 2.5:1 é call vs range balanceado do oponente",
    context: JSON.stringify({"situation": "river_bluffcatch"}),
    difficulty: "intermediate",
    category: "river_play",
    tags: JSON.stringify(["bluff_catch", "second_pair"])
  },

  {
    name: "Check-raise no Turn com Draw",
    nameEn: "Turn Check-raise with Draw",
    namePt: "Check-raise no Turn com Draw",
    description: "Você tem 9♠8♠ no board K♣7♠5♦6♠. Oponente aposta 40. Check-raise?",
    descriptionEn: "You have 9♠8♠ on K♣7♠5♦6♠ board. Opponent bets 40. Check-raise?",
    descriptionPt: "Você tem 9♠8♠ no board K♣7♠5♦6♠. Oponente aposta 40. Check-raise?",
    gameType: "cash",
    street: "turn",
    position: "BB",
    stackDepth: "deep",
    playerCards: JSON.stringify({"card1": "9s", "card2": "8s"}),
    communityCards: JSON.stringify({"flop": ["Kc", "7s", "5d"], "turn": "6s", "river": null}),
    potSize: 80,
    playerStack: 160,
    opponentActions: JSON.stringify([
      {"position": "BTN", "action": "bet", "amount": 40, "isActive": true, "stackSize": 170}
    ]),
    availableActions: JSON.stringify([
      {"action": "fold", "frequency": 0.0, "isCorrect": false, "explanation": "Foldar com straight draw + flush draw seria terrível"},
      {"action": "call", "frequency": 0.3, "isCorrect": false, "explanation": "Call é passivo com tantos outs"},
      {"action": "raise", "amount": 120, "frequency": 0.7, "isCorrect": true, "explanation": "Check-raise com combo draw tem fold equity + boa equity"}
    ]),
    correctAction: "raise",
    equity: 0.35,
    frequency: 0.7,
    explanation: "Combo draw (15 outs) justifica check-raise agressivo com fold equity",
    explanationEn: "Combo draw (15 outs) justifies aggressive check-raise with fold equity",
    explanationPt: "Combo draw (15 outs) justifica check-raise agressivo com fold equity",
    context: JSON.stringify({"situation": "combo_draw_checkraise"}),
    difficulty: "advanced",
    category: "turn_play",
    tags: JSON.stringify(["check_raise", "combo_draw"])
  },

  {
    name: "Fold vs 4-bet com AQo",
    nameEn: "Fold vs 4-bet with AQo",
    namePt: "Fold vs 4-bet com AQo",
    description: "Você fez 3-bet com AQ do CO. UTG fez 4-bet para 120bb. Como proceder?",
    descriptionEn: "You 3-bet with AQ from CO. UTG 4-bet to 120bb. How to proceed?",
    descriptionPt: "Você fez 3-bet com AQ do CO. UTG fez 4-bet para 120bb. Como proceder?",
    gameType: "cash",
    street: "preflop",
    position: "CO",
    stackDepth: "deep",
    playerCards: JSON.stringify({"card1": "Ah", "card2": "Qc"}),
    communityCards: JSON.stringify({"flop": null, "turn": null, "river": null}),
    potSize: 135,
    playerStack: 80,
    opponentActions: JSON.stringify([
      {"position": "UTG", "action": "raise", "amount": 120, "isActive": true, "stackSize": 80}
    ]),
    availableActions: JSON.stringify([
      {"action": "fold", "frequency": 0.8, "isCorrect": true, "explanation": "AQo vs 4-bet de UTG é fold padrão"},
      {"action": "call", "frequency": 0.1, "isCorrect": false, "explanation": "Call seria muito loose vs range apertado"},
      {"action": "raise", "frequency": 0.1, "isCorrect": false, "explanation": "5-bet seria suicídio com AQo"}
    ]),
    correctAction: "fold",
    equity: 0.35,
    frequency: 0.8,
    explanation: "AQo não tem equity suficiente vs range de 4-bet apertado do UTG",
    explanationEn: "AQo doesn't have enough equity vs UTG's tight 4-bet range",
    explanationPt: "AQo não tem equity suficiente vs range de 4-bet apertado do UTG",
    context: JSON.stringify({"situation": "4bet_defense"}),
    difficulty: "intermediate",
    category: "preflop",
    tags: JSON.stringify(["4bet_defense", "fold"])
  }
]

async function populateRealScenarios() {
  try {
    console.log('🗑️ Removendo todos os cenários antigos...')
    await prisma.gTOScenario.deleteMany()
    
    console.log('🎯 Criando 5 cenários realistas e coerentes...\n')
    
    for (let i = 0; i < realScenarios.length; i++) {
      const scenario = realScenarios[i]
      
      console.log(`📋 Verificando cenário ${i + 1}: ${scenario.name}`)
      
      // Verificar coerência dos dados
      const playerCards = JSON.parse(scenario.playerCards)
      const communityCards = JSON.parse(scenario.communityCards)
      
      console.log(`   Cartas do jogador: ${playerCards.card1}, ${playerCards.card2}`)
      console.log(`   Street: ${scenario.street}`)
      console.log(`   Posição: ${scenario.position}`)
      
      if (scenario.street !== 'preflop') {
        console.log(`   Board: ${communityCards.flop?.join(', ') || 'N/A'}`)
        if (communityCards.turn) console.log(`   Turn: ${communityCards.turn}`)
        if (communityCards.river) console.log(`   River: ${communityCards.river}`)
      }
      
      await prisma.gTOScenario.create({
        data: scenario
      })
      
      console.log(`   ✅ Cenário criado com sucesso!\n`)
    }
    
    console.log(`🎉 ${realScenarios.length} cenários realistas criados!`)
    console.log('\n📊 Resumo dos cenários:')
    realScenarios.forEach((s, i) => {
      console.log(`${i + 1}. ${s.name} (${s.difficulty}) - ${s.street}`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao criar cenários:', error)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  populateRealScenarios()
}

module.exports = { populateRealScenarios }
{"name": "gto-poker-trainer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:populate": "tsx scripts/populate-scenarios.ts", "test": "jest", "test:watch": "jest --watch"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.22.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "clsx": "^2.0.0", "framer-motion": "^10.16.4", "lucide-react": "^0.292.0", "mysql2": "^3.14.2", "next": "14.0.0", "postcss": "^8", "prisma": "^5.6.0", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tsx": "^4.20.3"}}
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { Card, PokerAction, GameState } from '@/types'
import { RANKS, SUITS, LEVELS } from './constants'

// Utility para combinar classes CSS
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utilitários de poker
export function createDeck(): Card[] {
  const deck: Card[] = []
  
  for (const suit of SUITS) {
    for (let i = 0; i < RANKS.length; i++) {
      const rank = RANKS[i]
      let value: number
      
      if (rank === 'A') value = 14
      else if (rank === 'K') value = 13
      else if (rank === 'Q') value = 12
      else if (rank === 'J') value = 11
      else if (rank === 'T') value = 10
      else value = parseInt(rank)
      
      deck.push({ suit, rank, value })
    }
  }
  
  return deck
}

export function shuffleDeck(deck: Card[]): Card[] {
  const shuffled = [...deck]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export function dealCards(deck: Card[], count: number): { cards: Card[], remainingDeck: Card[] } {
  const cards = deck.slice(0, count)
  const remainingDeck = deck.slice(count)
  return { cards, remainingDeck }
}

export function cardToString(card: Card): string {
  return `${card.rank}${card.suit[0].toLowerCase()}`
}

export function stringToCard(cardString: string): Card {
  const rank = cardString[0] as Card['rank']
  const suitChar = cardString[1].toLowerCase()
  
  let suit: Card['suit']
  switch (suitChar) {
    case 'h': suit = 'hearts'; break
    case 'd': suit = 'diamonds'; break
    case 'c': suit = 'clubs'; break
    case 's': suit = 'spades'; break
    default: throw new Error(`Invalid suit: ${suitChar}`)
  }
  
  let value: number
  if (rank === 'A') value = 14
  else if (rank === 'K') value = 13
  else if (rank === 'Q') value = 12
  else if (rank === 'J') value = 11
  else if (rank === 'T') value = 10
  else value = parseInt(rank)
  
  return { suit, rank, value }
}

// Cálculo de equidade básico (simplificado)
export function calculateEquity(playerCards: Card[], communityCards: Card[], opponentRange: string[]): number {
  // Implementação simplificada - em produção usaria uma biblioteca especializada
  const playerHand = [...playerCards, ...communityCards]
  const handStrength = evaluateHandStrength(playerHand)
  
  // Simulação básica contra range do oponente
  let wins = 0
  const simulations = 1000
  
  for (let i = 0; i < simulations; i++) {
    const randomOpponentHand = generateRandomHandFromRange(opponentRange)
    const opponentHandStrength = evaluateHandStrength([...randomOpponentHand, ...communityCards])
    
    if (handStrength > opponentHandStrength) {
      wins++
    }
  }
  
  return wins / simulations
}

function evaluateHandStrength(cards: Card[]): number {
  // Implementação muito simplificada - apenas soma dos valores das cartas
  // Em produção, implementaria avaliação completa de mãos de poker
  return cards.reduce((sum, card) => sum + card.value, 0)
}

function generateRandomHandFromRange(range: string[]): Card[] {
  // Implementação simplificada
  const randomHand = range[Math.floor(Math.random() * range.length)]
  // Converter string da mão para cartas reais
  return [
    stringToCard(randomHand.slice(0, 2)),
    stringToCard(randomHand.slice(2, 4))
  ]
}

// Utilitários de nível e XP
export function getLevelFromXP(xp: number): number {
  for (let i = LEVELS.length - 1; i >= 0; i--) {
    if (xp >= LEVELS[i].xpRequired) {
      return LEVELS[i].level
    }
  }
  return 1
}

export function getXPForNextLevel(currentXP: number): number {
  const currentLevel = getLevelFromXP(currentXP)
  const nextLevel = LEVELS.find(l => l.level === currentLevel + 1)
  return nextLevel ? nextLevel.xpRequired - currentXP : 0
}

export function getLevelProgress(currentXP: number): number {
  const currentLevel = getLevelFromXP(currentXP)
  const currentLevelData = LEVELS.find(l => l.level === currentLevel)
  const nextLevelData = LEVELS.find(l => l.level === currentLevel + 1)
  
  if (!currentLevelData || !nextLevelData) return 100
  
  const progressXP = currentXP - currentLevelData.xpRequired
  const totalXPNeeded = nextLevelData.xpRequired - currentLevelData.xpRequired
  
  return (progressXP / totalXPNeeded) * 100
}

// Utilitários de formatação
export function formatCurrency(amount: number, currency: string = '$'): string {
  return `${currency}${amount.toLocaleString()}`
}

export function formatPercentage(value: number): string {
  return `${(value * 100).toFixed(1)}%`
}

export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Utilitários de validação
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPassword(password: string): boolean {
  return password.length >= 8
}

export function isValidUsername(username: string): boolean {
  return username.length >= 3 && /^[a-zA-Z0-9_]+$/.test(username)
}

// Utilitários de ação GTO
export function getActionRecommendation(gameState: GameState): PokerAction {
  // Implementação simplificada - em produção usaria solver GTO real
  const { street, position, pot, stackSize } = gameState
  
  // Lógica básica baseada em posição e street
  if (street === 'preflop') {
    if (position.name === 'BTN' || position.name === 'CO') {
      return { type: 'raise', amount: pot * 0.75, sizing: 'medium' }
    } else if (position.name === 'BB') {
      return { type: 'call' }
    } else {
      return { type: 'fold' }
    }
  }
  
  // Pós-flop simplificado
  return { type: 'check' }
}

export function calculateOptimalBetSize(pot: number, stackSize: number, street: string): number {
  // Implementação simplificada
  const potPercentages = {
    flop: 0.75,
    turn: 0.66,
    river: 0.5
  }
  
  const percentage = potPercentages[street as keyof typeof potPercentages] || 0.66
  const betSize = pot * percentage
  
  return Math.min(betSize, stackSize)
}

// Utilitários de range
export function expandHandRange(range: string): string[] {
  // Implementação simplificada para expandir ranges como "AA", "AKs", "22+"
  const hands: string[] = []
  
  if (range.includes('+')) {
    // Implementar expansão de ranges como "22+"
    // Por simplicidade, retornando array básico
    return ['AA', 'KK', 'QQ', 'JJ', 'TT']
  }
  
  if (range.includes('s')) {
    // Mão suited
    hands.push(range)
  } else if (range.includes('o')) {
    // Mão offsuit
    hands.push(range)
  } else {
    // Par
    hands.push(range)
  }
  
  return hands
}

// Utilitários de estatísticas
export function calculateWinRate(wins: number, total: number): number {
  return total > 0 ? wins / total : 0
}

export function calculateROI(profit: number, investment: number): number {
  return investment > 0 ? (profit / investment) * 100 : 0
}

// Utilitários de tempo
export function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'agora'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m atrás`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h atrás`
  return `${Math.floor(diffInSeconds / 86400)}d atrás`
}

// Utilitários de localStorage
export function saveToLocalStorage(key: string, value: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export function loadFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return defaultValue
  }
}

export function removeFromLocalStorage(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing from localStorage:', error)
  }
}
'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { 
  Home, 
  Target, 
  BarChart3, 
  User, 
  Trophy, 
  Zap
} from 'lucide-react'
import LanguageToggle from '@/components/ui/LanguageToggle'
import { useLanguage } from '@/hooks/useLanguage'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { t } = useLanguage()
  
  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      current: pathname === '/dashboard'
    },
    {
      name: t('nav.training'),
      href: '/dashboard/training',
      icon: Target,
      current: pathname.startsWith('/dashboard/training')
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      href: '/dashboard/scenarios',
      icon: Target,
      current: pathname.startsWith('/dashboard/scenarios')
    },
    {
      name: t('nav.statistics'),
      href: '/dashboard/statistics',
      icon: BarChart3,
      current: pathname === '/dashboard/statistics'
    },
    {
      name: t('nav.achievements'),
      href: '/dashboard/achievements',
      icon: Trophy,
      current: pathname === '/dashboard/achievements'
    },
    {
      name: t('nav.profile'),
      href: '/dashboard/profile',
      icon: User,
      current: pathname === '/dashboard/profile'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold text-white">GTO Trainer</span>
            </Link>

            {/* User Info */}
            <div className="flex items-center space-x-4">
              <span className="text-white font-medium">GTO Trainer</span>
              <LanguageToggle />
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-gray-800 min-h-screen border-r border-gray-700">
          <div className="p-6">
            <div className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      item.current
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.name}</span>
                  </Link>
                )
              })}
            </div>


          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  )
}
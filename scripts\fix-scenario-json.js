const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function fixScenarioJSON() {
  try {
    console.log('🔍 Verificando cenários com JSON malformado...')
    
    const scenarios = await prisma.gTOScenario.findMany()
    
    for (const scenario of scenarios) {
      console.log(`\n📋 Cenário: ${scenario.name}`)
      
      // Verificar cada campo JSON
      const jsonFields = [
        'playerCards',
        'communityCards', 
        'opponentActions',
        'availableActions',
        'context'
      ]
      
      for (const field of jsonFields) {
        try {
          JSON.parse(scenario[field])
          console.log(`  ✅ ${field}: OK`)
        } catch (error) {
          console.log(`  ❌ ${field}: ERRO - ${error.message}`)
          console.log(`     Valor: ${scenario[field]}`)
          
          // Tentar corrigir valores comuns
          let fixedValue = scenario[field]
          
          if (field === 'playerCards') {
            fixedValue = '{"card1": "As", "card2": "Kh"}'
          } else if (field === 'communityCards') {
            fixedValue = '{"flop": ["9h", "7c", "2d"], "turn": null, "river": null}'
          } else if (field === 'opponentActions') {
            fixedValue = '[]'
          } else if (field === 'availableActions') {
            fixedValue = '[{"action": "fold", "frequency": 0.3}, {"action": "call", "frequency": 0.4}, {"action": "raise", "frequency": 0.3}]'
          } else if (field === 'context') {
            fixedValue = '{"notes": "Situação padrão"}'
          }
          
          // Atualizar no banco
          await prisma.gTOScenario.update({
            where: { id: scenario.id },
            data: { [field]: fixedValue }
          })
          
          console.log(`     ✅ Corrigido para: ${fixedValue}`)
        }
      }
    }
    
    console.log('\n✅ Verificação e correção concluída!')
    
  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixScenarioJSON()
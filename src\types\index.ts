// Tipos principais da aplicação

export interface User {
  id: string
  username: string
  language: 'pt' | 'en'
  level: number
  xp: number
  totalPoints: number
  createdAt: Date
  updatedAt: Date
}

export interface Card {
  suit: 'hearts' | 'diamonds' | 'clubs' | 'spades'
  rank: 'A' | 'K' | 'Q' | 'J' | 'T' | '9' | '8' | '7' | '6' | '5' | '4' | '3' | '2'
  value: number
}

export interface HandRange {
  hands: string[]
  frequency: number
  action: PokerAction
}

export interface PokerPosition {
  name: 'UTG' | 'MP' | 'CO' | 'BTN' | 'SB' | 'BB'
  displayName: string
  order: number
}

export interface PokerAction {
  type: 'fold' | 'call' | 'raise' | 'check' | 'bet' | 'allin'
  amount?: number
  sizing?: 'small' | 'medium' | 'large' | 'overbet'
}

export interface PlayerAction {
  position: string
  action: PokerAction
  amount?: number
  isActive: boolean
  stackSize: number
}

export interface GameState {
  street: 'preflop' | 'flop' | 'turn' | 'river'
  pot: number
  communityCards: Card[]
  playerCards: Card[]
  position: PokerPosition
  stackSize: number
  gameType: 'cash' | 'tournament'
  playerActions: PlayerAction[] // Ações de todos os jogadores
  actionToUser: boolean // Se é a vez do usuário agir
  lastAction?: PokerAction // Última ação do jogador para comparação
}

export interface GTORecommendation {
  action: PokerAction
  frequency: number
  equity: number
  explanation: string
  alternativeActions?: {
    action: PokerAction
    frequency: number
  }[]
}

export interface TrainingScenario {
  id: string
  name: string
  description: string
  gameState: GameState
  gtoRecommendation: GTORecommendation
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  category: string
  tags: string[]
}

export interface QuizQuestion {
  id: string
  scenario: TrainingScenario
  options: PokerAction[]
  correctAnswer: PokerAction
  explanation: string
  timeLimit?: number
}

export interface TrainingSession {
  id: string
  userId: string
  sessionType: 'quiz' | 'simulation' | 'practice'
  difficulty: string
  score: number
  totalQuestions: number
  correctAnswers: number
  xpGained: number
  duration: number
  completed: boolean
  createdAt: Date
}

export interface HandHistory {
  id: string
  userId: string
  gameType: 'cash' | 'tournament'
  position: string
  holeCards: string[]
  communityCards?: string[]
  potSize: number
  stackSize: number
  action: string
  betSize?: number
  isCorrect: boolean
  gtoAction: string
  gtoBetSize?: number
  equity?: number
  explanation?: string
  street: string
  createdAt: Date
}

export interface Achievement {
  id: string
  name: string
  nameEn: string
  namePt: string
  description: string
  descriptionEn: string
  descriptionPt: string
  icon: string
  xpReward: number
  category: string
  requirement: any
}

export interface UserStatistics {
  id: string
  userId: string
  totalHands: number
  correctDecisions: number
  averageEquity: number
  preflopAccuracy: number
  flopAccuracy: number
  turnAccuracy: number
  riverAccuracy: number
  aggressionFrequency: number
  foldFrequency: number
  callFrequency: number
  raiseFrequency: number
  cBetFrequency: number
  checkRaiseFrequency: number
  currentStreak: number
  longestStreak: number
  updatedAt: Date
}

export interface LeaderboardEntry {
  userId: string
  username: string
  level: number
  totalPoints: number
  rank: number
}

// Tipos para componentes
export interface PokerTableProps {
  gameState: GameState
  onAction: (action: PokerAction) => void
  showRecommendation?: boolean
  recommendation?: GTORecommendation
  availableActions?: any[] // Ações disponíveis do cenário
  onNewSituation?: () => void
  onTryAgain?: () => void
}

export interface CardProps {
  card: Card
  faceUp?: boolean
  size?: 'small' | 'medium' | 'large'
  className?: string
}

export interface ChipStackProps {
  amount: number
  size?: 'small' | 'medium' | 'large'
  className?: string
}

// Tipos para API
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}



// Tipos para internacionalização
export interface TranslationKeys {
  [key: string]: string | TranslationKeys
}

export interface LanguageContextType {
  language: 'pt' | 'en'
  setLanguage: (lang: 'pt' | 'en') => void
  t: (key: string) => string
}
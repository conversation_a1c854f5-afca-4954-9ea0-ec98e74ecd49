'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  Save, 
  ArrowLeft, 
  Plus, 
  Trash2, 
  Eye,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { useLanguage } from '@/hooks/useLanguage'

interface PlayerCard {
  card1: string
  card2: string
}

interface CommunityCards {
  flop: string[] | null
  turn: string | null
  river: string | null
}

interface OpponentAction {
  position: string
  action: string
  amount?: number
  isActive: boolean
  stackSize: number
}

interface AvailableAction {
  action: string
  amount?: number
  frequency: number
  isCorrect: boolean
  explanation: string
}

export default function CreateScenarioPage() {
  const { t } = useLanguage()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // Upload state
  const [uploadFile, setUploadFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResults, setUploadResults] = useState<{success: number, errors: string[]} | null>(null)
  
  // Custom filter state
  const [customFilters, setCustomFilters] = useState<any[]>([])
  const [showCreateFilterDialog, setShowCreateFilterDialog] = useState(false)
  const [pendingFilterName, setPendingFilterName] = useState('')

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    gameType: 'cash',
    street: 'preflop',
    position: 'UTG',
    stackDepth: 'deep',
    potSize: 0,
    playerStack: 200,
    correctAction: 'fold',
    equity: 0.5,
    frequency: 1.0,
    explanation: '',
    difficulty: 'intermediate',
    category: 'general',
    tags: [] as string[],
    customFilter: '',
    customFilterDescription: '',
    customFilterColor: '#3B82F6'
  })

  const [playerCards, setPlayerCards] = useState<PlayerCard>({
    card1: '',
    card2: ''
  })

  const [communityCards, setCommunityCards] = useState<CommunityCards>({
    flop: null,
    turn: null,
    river: null
  })

  const [opponentActions, setOpponentActions] = useState<OpponentAction[]>([])
  const [availableActions, setAvailableActions] = useState<AvailableAction[]>([
    {
      action: 'fold',
      frequency: 0.3,
      isCorrect: false,
      explanation: ''
    }
  ])

  const positions = ['UTG', 'MP', 'CO', 'BTN', 'SB', 'BB']
  const streets = ['preflop', 'flop', 'turn', 'river']
  const gameTypes = ['cash', 'tournament']
  const difficulties = ['beginner', 'intermediate', 'advanced']
  const actions = ['fold', 'call', 'raise', 'check', 'bet']
  const suits = ['s', 'h', 'd', 'c']
  const ranks = ['A', 'K', 'Q', 'J', 'T', '9', '8', '7', '6', '5', '4', '3', '2']

  const addOpponentAction = () => {
    setOpponentActions([...opponentActions, {
      position: 'BB',
      action: 'check',
      isActive: true,
      stackSize: 200
    }])
  }

  const removeOpponentAction = (index: number) => {
    setOpponentActions(opponentActions.filter((_, i) => i !== index))
  }

  const addAvailableAction = () => {
    setAvailableActions([...availableActions, {
      action: 'call',
      frequency: 0.3,
      isCorrect: false,
      explanation: ''
    }])
  }

  // Carregar filtros personalizados
  const loadCustomFilters = async () => {
    try {
      const response = await fetch('/api/custom-filters')
      if (response.ok) {
        const data = await response.json()
        setCustomFilters(data.filters || [])
      }
    } catch (error) {
      console.error('Erro ao carregar filtros:', error)
    }
  }

  // Carregar filtros ao montar o componente
  React.useEffect(() => {
    loadCustomFilters()
  }, [])

  const removeAvailableAction = (index: number) => {
    setAvailableActions(availableActions.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      // Validações básicas
      if (!playerCards.card1 || !playerCards.card2) {
        throw new Error('Cartas do jogador são obrigatórias')
      }

      if (availableActions.length === 0) {
        throw new Error('Pelo menos uma ação disponível é obrigatória')
      }

      const payload = {
        ...formData,
        playerCards,
        communityCards,
        opponentActions,
        availableActions,
        tags: formData.tags.filter(tag => tag.trim() !== ''),
        customFilter: formData.customFilter || null
      }

      const response = await fetch('/api/scenarios/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (!response.ok) {
        // Se o filtro não existe, perguntar se deseja criar
        if (result.error === 'CUSTOM_FILTER_NOT_EXISTS') {
          setPendingFilterName(result.filterName)
          setShowCreateFilterDialog(true)
          return
        }
        throw new Error(result.error || 'Erro ao criar cenário')
      }

      setSuccess('Cenário criado com sucesso!')
      setTimeout(() => {
        router.push('/dashboard/scenarios')
      }, 2000)

    } catch (err) {
      setError((err as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setUploadFile(file)
      setUploadResults(null)
    }
  }

  const uploadJsonFile = async () => {
    if (!uploadFile) return

    setIsUploading(true)
    setError('')
    setUploadResults(null)

    try {
      const formData = new FormData()
      formData.append('file', uploadFile)

      const response = await fetch('/api/scenarios/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro no upload')
      }

      setUploadResults(result.results)
      setUploadFile(null)
      
      if (result.results.success > 0) {
        setSuccess(`${result.results.success} cenários importados com sucesso!`)
      }

    } catch (err) {
      setError((err as Error).message)
    } finally {
      setIsUploading(false)
    }
  }

  const createCustomFilter = async () => {
    try {
      const response = await fetch('/api/custom-filters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: pendingFilterName,
          description: formData.customFilterDescription || `Filtro personalizado: ${pendingFilterName}`,
          color: formData.customFilterColor
        })
      })

      const result = await response.json()

      if (response.ok) {
        // Recarregar filtros
        await loadCustomFilters()
        
        // Definir o filtro criado como selecionado
        setFormData({...formData, customFilter: pendingFilterName})
        
        // Fechar dialog
        setShowCreateFilterDialog(false)
        setPendingFilterName('')
        
        // Tentar criar o cenário novamente
        handleSubmit(new Event('submit') as any)
      } else {
        setError(result.error || 'Erro ao criar filtro personalizado')
      }
    } catch (error) {
      setError('Erro ao criar filtro personalizado')
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link 
              href="/dashboard" 
              className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Dashboard
            </Link>
            <div className="w-px h-6 bg-gray-600"></div>
            <h1 className="text-2xl font-bold text-white">Criar Novo Cenário</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <a
              href="/scenarios-template.json"
              download
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
            >
              <span>📄 Template JSON</span>
            </a>
            
            <a
              href="/COMO_CRIAR_CENARIOS.md"
              download
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
            >
              <span>📖 Instruções</span>
            </a>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6 flex items-center"
          >
            <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
            <span className="text-red-300">{error}</span>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-900/20 border border-green-500 rounded-lg p-4 mb-6 flex items-center"
          >
            <span className="text-green-300">{success}</span>
          </motion.div>
        )}

        {/* Upload JSON Section */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 mb-8">
          <h2 className="text-xl font-bold text-white mb-4">Upload de Cenários via JSON</h2>
          <p className="text-gray-400 mb-4">
            Importe múltiplos cenários de uma vez usando um arquivo JSON. 
            Baixe o template e as instruções acima para criar cenários realistas.
          </p>
          
          <div className="flex items-center space-x-4">
            <input
              type="file"
              accept=".json"
              onChange={handleFileUpload}
              className="hidden"
              id="json-upload"
            />
            <label
              htmlFor="json-upload"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors cursor-pointer flex items-center space-x-2"
            >
              <span>📁 Selecionar Arquivo JSON</span>
            </label>
            
            {uploadFile && (
              <div className="flex items-center space-x-2">
                <span className="text-gray-300">{uploadFile.name}</span>
                <button
                  type="button"
                  onClick={uploadJsonFile}
                  disabled={isUploading}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isUploading ? 'Enviando...' : 'Enviar'}
                </button>
                <button
                  type="button"
                  onClick={() => setUploadFile(null)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
              </div>
            )}
          </div>
          
          {uploadResults && (
            <div className="mt-4 p-4 bg-gray-700 rounded-lg">
              <div className="text-green-400 font-medium mb-2">
                ✅ {uploadResults.success} cenários importados com sucesso
              </div>
              {uploadResults.errors.length > 0 && (
                <div className="text-red-400">
                  <div className="font-medium mb-1">Erros encontrados:</div>
                  <ul className="text-sm space-y-1">
                    {uploadResults.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Informações Básicas */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-white mb-6">Informações Básicas</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Nome do Cenário *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: C-bet no Flop Seco com AK"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Categoria
                </label>
                <input
                  type="text"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: cbet, 3bet, bluff_catch"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Descrição *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Descreva a situação do cenário..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Tipo de Jogo
                </label>
                <select
                  value={formData.gameType}
                  onChange={(e) => setFormData({...formData, gameType: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {gameTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Street
                </label>
                <select
                  value={formData.street}
                  onChange={(e) => setFormData({...formData, street: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {streets.map(street => (
                    <option key={street} value={street}>{street}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Posição
                </label>
                <select
                  value={formData.position}
                  onChange={(e) => setFormData({...formData, position: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {positions.map(pos => (
                    <option key={pos} value={pos}>{pos}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Dificuldade
                </label>
                <select
                  value={formData.difficulty}
                  onChange={(e) => setFormData({...formData, difficulty: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {difficulties.map(diff => (
                    <option key={diff} value={diff}>{diff}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Filtro Personalizado */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-white mb-6">Filtro Personalizado</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Filtro Personalizado
                </label>
                <div className="flex space-x-2">
                  <select
                    value={formData.customFilter}
                    onChange={(e) => setFormData({...formData, customFilter: e.target.value})}
                    className="flex-1 bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Nenhum filtro</option>
                    {customFilters.map(filter => (
                      <option key={filter.id} value={filter.name}>{filter.name}</option>
                    ))}
                  </select>
                  
                  <input
                    type="text"
                    value={formData.customFilter}
                    onChange={(e) => setFormData({...formData, customFilter: e.target.value})}
                    className="flex-1 bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ou digite um novo filtro"
                  />
                </div>
                <p className="text-sm text-gray-400 mt-1">
                  Selecione um filtro existente ou digite um novo para criar
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Cor do Filtro
                </label>
                <input
                  type="color"
                  value={formData.customFilterColor}
                  onChange={(e) => setFormData({...formData, customFilterColor: e.target.value})}
                  className="w-full h-10 bg-gray-700 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Descrição do Filtro
                </label>
                <input
                  type="text"
                  value={formData.customFilterDescription}
                  onChange={(e) => setFormData({...formData, customFilterDescription: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional do filtro personalizado"
                />
              </div>
            </div>
          </div>

          {/* Cartas do Jogador */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-white mb-6">Cartas do Jogador</h2>
            
            <div className="grid grid-cols-2 gap-4 max-w-md">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Carta 1 *
                </label>
                <input
                  type="text"
                  value={playerCards.card1}
                  onChange={(e) => setPlayerCards({...playerCards, card1: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: As"
                  maxLength={2}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Carta 2 *
                </label>
                <input
                  type="text"
                  value={playerCards.card2}
                  onChange={(e) => setPlayerCards({...playerCards, card2: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Kh"
                  maxLength={2}
                  required
                />
              </div>
            </div>
            
            <p className="text-sm text-gray-400 mt-2">
              Formato: Rank + Suit (Ex: As, Kh, Qd, Jc)
            </p>
          </div>

          {/* Community Cards */}
          {formData.street !== 'preflop' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-xl font-bold text-white mb-6">Cartas Comunitárias</h2>
              
              {/* Flop */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Flop
                </label>
                <div className="grid grid-cols-3 gap-2 max-w-md">
                  {[0, 1, 2].map(i => (
                    <input
                      key={i}
                      type="text"
                      value={communityCards.flop?.[i] || ''}
                      onChange={(e) => {
                        const newFlop = communityCards.flop || ['', '', '']
                        newFlop[i] = e.target.value
                        setCommunityCards({...communityCards, flop: newFlop})
                      }}
                      className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`Carta ${i + 1}`}
                      maxLength={2}
                    />
                  ))}
                </div>
              </div>

              {/* Turn */}
              {(formData.street === 'turn' || formData.street === 'river') && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Turn
                  </label>
                  <input
                    type="text"
                    value={communityCards.turn || ''}
                    onChange={(e) => setCommunityCards({...communityCards, turn: e.target.value})}
                    className="w-24 bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Turn"
                    maxLength={2}
                  />
                </div>
              )}

              {/* River */}
              {formData.street === 'river' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    River
                  </label>
                  <input
                    type="text"
                    value={communityCards.river || ''}
                    onChange={(e) => setCommunityCards({...communityCards, river: e.target.value})}
                    className="w-24 bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="River"
                    maxLength={2}
                  />
                </div>
              )}
            </div>
          )}

          {/* Informações do Pot */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-white mb-6">Informações do Pot</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Tamanho do Pot
                </label>
                <input
                  type="number"
                  value={formData.potSize}
                  onChange={(e) => setFormData({...formData, potSize: parseFloat(e.target.value) || 0})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="0.5"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Stack do Jogador
                </label>
                <input
                  type="number"
                  value={formData.playerStack}
                  onChange={(e) => setFormData({...formData, playerStack: parseFloat(e.target.value) || 200})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="0.5"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Profundidade do Stack
                </label>
                <select
                  value={formData.stackDepth}
                  onChange={(e) => setFormData({...formData, stackDepth: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="shallow">Shallow</option>
                  <option value="medium">Medium</option>
                  <option value="deep">Deep</option>
                </select>
              </div>
            </div>
          </div>

          {/* Ações dos Oponentes */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Ações dos Oponentes</h2>
              <button
                type="button"
                onClick={addOpponentAction}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Adicionar Ação</span>
              </button>
            </div>

            <div className="space-y-4">
              {opponentActions.map((action, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Posição
                      </label>
                      <select
                        value={action.position}
                        onChange={(e) => {
                          const newActions = [...opponentActions]
                          newActions[index].position = e.target.value
                          setOpponentActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {positions.map(pos => (
                          <option key={pos} value={pos}>{pos}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Ação
                      </label>
                      <select
                        value={action.action}
                        onChange={(e) => {
                          const newActions = [...opponentActions]
                          newActions[index].action = e.target.value
                          setOpponentActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {actions.map(act => (
                          <option key={act} value={act}>{act}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Valor
                      </label>
                      <input
                        type="number"
                        value={action.amount || ''}
                        onChange={(e) => {
                          const newActions = [...opponentActions]
                          newActions[index].amount = parseFloat(e.target.value) || undefined
                          setOpponentActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        step="0.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Stack
                      </label>
                      <input
                        type="number"
                        value={action.stackSize}
                        onChange={(e) => {
                          const newActions = [...opponentActions]
                          newActions[index].stackSize = parseFloat(e.target.value) || 200
                          setOpponentActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        step="0.5"
                      />
                    </div>

                    <div className="flex items-end">
                      <button
                        type="button"
                        onClick={() => removeOpponentAction(index)}
                        className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Ações Disponíveis */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Ações Disponíveis</h2>
              <button
                type="button"
                onClick={addAvailableAction}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Adicionar Ação</span>
              </button>
            </div>

            <div className="space-y-4">
              {availableActions.map((action, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Ação
                      </label>
                      <select
                        value={action.action}
                        onChange={(e) => {
                          const newActions = [...availableActions]
                          newActions[index].action = e.target.value
                          setAvailableActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {actions.map(act => (
                          <option key={act} value={act}>{act}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Valor
                      </label>
                      <input
                        type="number"
                        value={action.amount || ''}
                        onChange={(e) => {
                          const newActions = [...availableActions]
                          newActions[index].amount = parseFloat(e.target.value) || undefined
                          setAvailableActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        step="0.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Frequência (0-1)
                      </label>
                      <input
                        type="number"
                        value={action.frequency}
                        onChange={(e) => {
                          const newActions = [...availableActions]
                          newActions[index].frequency = parseFloat(e.target.value) || 0
                          setAvailableActions(newActions)
                        }}
                        className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max="1"
                        step="0.1"
                      />
                    </div>

                    <div className="flex items-center">
                      <label className="flex items-center space-x-2 text-gray-300">
                        <input
                          type="checkbox"
                          checked={action.isCorrect}
                          onChange={(e) => {
                            const newActions = [...availableActions]
                            newActions[index].isCorrect = e.target.checked
                            setAvailableActions(newActions)
                          }}
                          className="rounded border-gray-500 bg-gray-600 text-blue-600 focus:ring-blue-500"
                        />
                        <span>Ação Correta</span>
                      </label>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Explicação
                    </label>
                    <textarea
                      value={action.explanation}
                      onChange={(e) => {
                        const newActions = [...availableActions]
                        newActions[index].explanation = e.target.value
                        setAvailableActions(newActions)
                      }}
                      className="w-full bg-gray-600 text-white rounded-lg px-3 py-2 border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={2}
                      placeholder="Explique por que esta ação é correta ou incorreta..."
                    />
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => removeAvailableAction(index)}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Remover</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Análise GTO */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-white mb-6">Análise GTO</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Ação Correta
                </label>
                <select
                  value={formData.correctAction}
                  onChange={(e) => setFormData({...formData, correctAction: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {actions.map(action => (
                    <option key={action} value={action}>{action}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Equity (0-1)
                </label>
                <input
                  type="number"
                  value={formData.equity}
                  onChange={(e) => setFormData({...formData, equity: parseFloat(e.target.value) || 0.5})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  max="1"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Frequência (0-1)
                </label>
                <input
                  type="number"
                  value={formData.frequency}
                  onChange={(e) => setFormData({...formData, frequency: parseFloat(e.target.value) || 1.0})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  max="1"
                  step="0.01"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Explicação GTO *
              </label>
              <textarea
                value={formData.explanation}
                onChange={(e) => setFormData({...formData, explanation: e.target.value})}
                className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="Explique por que esta é a jogada GTO correta..."
                required
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/dashboard"
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2"
            >
              <span>Cancelar</span>
            </Link>
            
            <button
              type="submit"
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2"
            >
              <Save className="w-5 h-5" />
              <span>{isLoading ? 'Salvando...' : 'Criar Cenário'}</span>
            </button>
          </div>
        </form>

        {/* Dialog para criar filtro personalizado */}
        {showCreateFilterDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold text-white mb-4">Criar Filtro Personalizado</h3>
              
              <p className="text-gray-300 mb-4">
                O filtro personalizado <strong>"{pendingFilterName}"</strong> não existe. 
                Deseja criá-lo?
              </p>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Descrição
                </label>
                <input
                  type="text"
                  value={formData.customFilterDescription}
                  onChange={(e) => setFormData({...formData, customFilterDescription: e.target.value})}
                  className="w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={`Filtro personalizado: ${pendingFilterName}`}
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Cor
                </label>
                <input
                  type="color"
                  value={formData.customFilterColor}
                  onChange={(e) => setFormData({...formData, customFilterColor: e.target.value})}
                  className="w-full h-10 bg-gray-700 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateFilterDialog(false)
                    setPendingFilterName('')
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                
                <button
                  type="button"
                  onClick={createCustomFilter}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Criar Filtro
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
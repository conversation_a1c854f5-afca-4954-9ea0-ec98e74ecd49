const fs = require('fs');

async function testUpload() {
  try {
    console.log('🧪 Testando upload de JSON...');
    
    // Verificar se o arquivo existe
    if (!fs.existsSync('./test-scenarios.json')) {
      console.log('❌ Arquivo test-scenarios.json não encontrado');
      return;
    }

    // Ler e validar o JSON
    const fileContent = fs.readFileSync('./test-scenarios.json', 'utf8');
    console.log('📄 Arquivo lido com sucesso');
    
    try {
      const scenarios = JSON.parse(fileContent);
      console.log(`✅ JSON válido com ${scenarios.length} cenários`);
      
      // Mostrar os nomes dos cenários
      scenarios.forEach((scenario, index) => {
        console.log(`  ${index + 1}. ${scenario.name}`);
      });
      
    } catch (parseError) {
      console.log('❌ Erro ao fazer parse do JSON:', parseError.message);
      return;
    }

    // Testar a requisição de upload
    console.log('\n🌐 Testando requisição de upload...');
    
    const formData = new FormData();
    const blob = new Blob([fileContent], { type: 'application/json' });
    formData.append('file', blob, 'test-scenarios.json');

    const response = await fetch('http://localhost:3000/api/scenarios/upload', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Upload realizado com sucesso!');
      console.log('📊 Resultado:', result);
    } else {
      console.log('❌ Erro no upload:', result.error);
    }

  } catch (error) {
    console.log('❌ Erro geral:', error.message);
  }
}

// Verificar se fetch está disponível
if (typeof fetch === 'undefined') {
  // Para Node.js < 18, usar node-fetch
  const fetch = require('node-fetch');
  global.fetch = fetch;
  global.FormData = require('form-data');
  global.Blob = require('buffer').Blob;
}

testUpload();
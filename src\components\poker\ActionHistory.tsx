'use client'

import { motion } from 'framer-motion'
import { PlayerAction } from '@/types'
import { cn } from '@/lib/utils'
import { useLanguage } from '@/hooks/useLanguage'

interface ActionHistoryProps {
  playerActions: PlayerAction[]
  currentPosition: string
}

export default function ActionHistory({ playerActions, currentPosition }: ActionHistoryProps) {
  const { t } = useLanguage()
  
  if (playerActions.length === 0) {
    return null
  }

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'fold':
        return 'text-red-400 bg-red-900/20 border-red-500/30'
      case 'call':
        return 'text-blue-400 bg-blue-900/20 border-blue-500/30'
      case 'raise':
        return 'text-green-400 bg-green-900/20 border-green-500/30'
      case 'bet':
        return 'text-orange-400 bg-orange-900/20 border-orange-500/30'
      case 'check':
        return 'text-gray-400 bg-gray-900/20 border-gray-500/30'
      default:
        return 'text-gray-400 bg-gray-900/20 border-gray-500/30'
    }
  }

  const getActionText = (action: PlayerAction) => {
    const baseText = action.action.type.toUpperCase()
    if (action.amount && action.amount > 0) {
      return `${baseText} $${action.amount}`
    }
    return baseText
  }

  return (
    <div className="bg-gray-800 rounded-xl p-4 border border-gray-700 mb-6">
      <h3 className="text-white font-semibold mb-3 flex items-center">
        <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
        {t('poker.tableActions')}
      </h3>
      
      <div className="space-y-2">
        {playerActions.map((playerAction, index) => (
          <motion.div
            key={`${playerAction.position}-${index}`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center justify-between p-3 rounded-lg bg-gray-700/50"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-white">
                  {playerAction.position}
                </span>
              </div>
              
              <div className="flex flex-col">
                <span className="text-white text-sm font-medium">
                  {playerAction.position}
                </span>
                <span className="text-gray-400 text-xs">
                  {t('poker.stack')}: ${playerAction.stackSize.toLocaleString()}
                </span>
              </div>
            </div>
            
            <div className={cn(
              'px-3 py-1 rounded-full border text-xs font-bold',
              getActionColor(playerAction.action.type)
            )}>
              {getActionText(playerAction)}
            </div>
          </motion.div>
        ))}
        
        {/* Indicador da vez do usuário */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: playerActions.length * 0.1 + 0.2 }}
          className="flex items-center justify-between p-3 rounded-lg bg-yellow-900/20 border border-yellow-500/30"
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center animate-pulse">
              <span className="text-xs font-bold text-black">
                {currentPosition}
              </span>
            </div>
            
            <div className="flex flex-col">
              <span className="text-yellow-400 text-sm font-medium">
                {currentPosition} ({t('poker.you')})
              </span>
              <span className="text-yellow-300 text-xs">
                {t('poker.yourTurnToAct')}
              </span>
            </div>
          </div>
          
          <div className="px-3 py-1 rounded-full border border-yellow-500/50 bg-yellow-500/20">
            <span className="text-yellow-400 text-xs font-bold animate-pulse">
              {t('poker.actionRequired')}
            </span>
          </div>
        </motion.div>
      </div>
    </div>
  )
}